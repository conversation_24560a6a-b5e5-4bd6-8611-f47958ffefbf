# US-3: Resume Upload - Dr. <PERSON>sume AI

This guide explains how to implement the resume upload functionality, allowing authenticated users to upload their resume files to the system.

---

## 1. Backend Implementation

### a. Resume Model
- **File:** `backend/models/resume_model.py`
- **Purpose:** Defines the database table structure for storing resume information, linking it to a user.
- **Key Steps:**
  1. Creates a `Resume` model with fields for `id`, `user_id` (foreign key to `User`), `file_path`, and `filename`.
  2. Establishes a relationship with the `User` model.
- **Code:**
```python
from sqlalchemy import Column, Integer, String, ForeignKey
from sqlalchemy.orm import relationship
from ..database import Base

class Resume(Base):
    __tablename__ = "resumes"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    file_path = Column(String, unique=True, index=True)
    filename = Column(String)

    owner = relationship("User")
```

### b. Upload Endpoint
- **File:** `backend/routers/upload.py`
- **Purpose:** Handles the file upload, saves the resume, and records its metadata in the database.
- **Key Steps:**
  1. Requires a valid JWT for authentication.
  2. Extracts the `user_id` from the JWT payload.
  3. Creates an `uploads` directory if it doesn't exist.
  4. Saves the uploaded file to the `uploads` directory.
  5. Creates a new `Resume` record in the database with the file path and filename.
- **Code:**
```python
from fastapi import APIRouter, File, UploadFile, HTTPException, status, Depends, Header
from sqlalchemy.orm import Session
from ..database import SessionLocal
from ..models.user_model import User
from ..models.resume_model import Resume
from .jwt_utils import verify_token
import shutil
import os

router = APIRouter()

def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

@router.post("/upload_resume")
def upload_resume(file: UploadFile = File(...), Authorization: str = Header(None), db: Session = Depends(get_db)):
    if not Authorization or not Authorization.startswith("Bearer "):
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Missing or invalid token")
    token = Authorization.split(" ")[1]
    payload = verify_token(token)
    if not payload:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid token")
    user_id = payload.get("user_id")

    # Create uploads directory if it doesn't exist
    upload_dir = "uploads"
    os.makedirs(upload_dir, exist_ok=True)

    file_location = os.path.join(upload_dir, file.filename)
    with open(file_location, "wb") as buffer:
        shutil.copyfileobj(file.file, buffer)
    
    new_resume = Resume(user_id=user_id, file_path=file_location, filename=file.filename)
    db.add(new_resume)
    db.commit()
    db.refresh(new_resume)

    return {"filename": file.filename, "message": "Resume uploaded successfully!"}
```

### c. Registering the Router
- **File:** `backend/main.py`
- **Purpose:** Integrates the resume upload endpoint and the new `Resume` model into the FastAPI application.
- **Code Addition:**
```python
from .models import user_model, resume_model
from .routers import auth, login, upload

user_model.Base.metadata.create_all(bind=engine)
resume_model.Base.metadata.create_all(bind=engine)

app.include_router(upload.router, prefix="/api", tags=["Resume Upload"])
```

---

## 2. Frontend Implementation

### a. Resume Upload Page
- **File:** `frontend/upload_resume.html`
- **Purpose:** Provides a form for users to select and upload their resume files.
- **Features:**
  - File input field that accepts PDF and common document formats.
  - An "Upload Resume" button.
  - Displays messages indicating upload success or failure.
  - Requires the user to be logged in (checks for JWT in `localStorage`).
- **Code:**
```html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Upload Resume - Dr. Resume</title>
</head>
<body>
    <h2>Upload Your Resume</h2>
    <form id="uploadForm">
        <input type="file" id="resumeFile" accept=".pdf,.doc,.docx" required><br><br>
        <button type="submit">Upload Resume</button>
    </form>
    <div id="message"></div>
    <script>
        document.getElementById('uploadForm').onsubmit = async function(e) {
            e.preventDefault();
            const resumeFile = document.getElementById('resumeFile').files[0];
            const token = localStorage.getItem('token');

            if (!resumeFile) {
                document.getElementById('message').innerText = 'Please select a file to upload.';
                return;
            }

            if (!token) {
                document.getElementById('message').innerText = 'You must be logged in to upload a resume.';
                return;
            }

            const formData = new FormData();
            formData.append('file', resumeFile);

            const res = await fetch('/api/upload_resume', {
                method: 'POST',
                headers: {
                    'Authorization': 'Bearer ' + token
                },
                body: formData
            });
            
            const data = await res.json();
            
            if (res.ok) {
                document.getElementById('message').innerText = data.message;
            } else {
                document.getElementById('message').innerText = data.detail || 'An error occurred during upload.';
            }
        };
    </script>
</body>
</html>
```

---

## 3. Integration & Flow
- The user navigates to the `upload_resume.html` page.
- They select a resume file and click "Upload Resume".
- The frontend creates a `FormData` object and sends a POST request to `/api/upload_resume` with the file and the user's JWT.
- The backend saves the file to the `uploads` directory and records the file details in the database.
- A success or error message is displayed to the user.

---

## 4. How to Build This Feature (Step-by-Step)
1. **Define the `Resume` model** in `backend/models/resume_model.py`.
2. **Update `backend/main.py`** to include `resume_model` metadata creation and register the `upload` router.
3. **Create the upload endpoint** in `backend/routers/upload.py`.
4. **Build the resume upload page** in `frontend/upload_resume.html`.
5. **Test the flow**: Log in, upload a resume, and verify that the file appears in the `uploads` directory and a record is created in the database.

---

## 5. Security Notes
- **Authentication:** Ensure only authenticated users can upload files by verifying the JWT.
- **File Type Validation:** While the frontend `accept` attribute helps, always perform server-side validation of file types to prevent malicious uploads.
- **File Size Limits:** Implement server-side file size limits to prevent denial-of-service attacks.
- **Directory Traversal:** Be careful when constructing file paths to prevent directory traversal vulnerabilities. `os.path.join` helps mitigate this.

---

This feature is crucial for the core functionality of Dr. Resume AI, allowing users to manage their resume documents.
