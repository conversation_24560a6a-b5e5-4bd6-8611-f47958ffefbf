from fastapi import FastAPI
from .database import engine
from .models import user_model, resume_model
from .routers import auth, login, upload

user_model.Base.metadata.create_all(bind=engine)
resume_model.Base.metadata.create_all(bind=engine)

app = FastAPI()

app.include_router(auth.router, prefix="/api", tags=["Authentication"])
app.include_router(login.router, prefix="/api", tags=["Authentication"])
app.include_router(upload.router, prefix="/api", tags=["Resume Upload"])

@app.get("/")
def read_root():
    return {"message": "Welcome to Dr. Resume AI"}