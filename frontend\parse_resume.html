<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Parse Resume - Dr. Resume</title>
</head>
<body>
    <h2>Parse Your Resume</h2>
    <form id="parseForm">
        <label for="resumeSelect">Select Resume to Parse:</label><br>
        <select id="resumeSelect" required>
            <!-- Resumes will be loaded here dynamically -->
        </select><br><br>

        <button type="submit">Parse Resume</button>
    </form>
    <div id="message"></div>
    <div id="parsedResult" style="margin-top: 20px;">
        <h3>Parsed Data:</h3>
        <pre id="parsedDataDisplay"></pre>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', async () => {
            const token = localStorage.getItem('token');
            if (!token) {
                document.getElementById('message').innerText = 'You must be logged in to use this feature.';
                return;
            }

            // Function to fetch and populate resumes
            async function loadResumes() {
                const res = await fetch('/api/resumes', {
                    headers: { 'Authorization': 'Bearer ' + token }
                });
                if (res.ok) {
                    const data = await res.json();
                    const resumeSelect = document.getElementById('resumeSelect');
                    resumeSelect.innerHTML = '';
                    if (data.resumes.length === 0) {
                        document.getElementById('message').innerText = 'No resumes uploaded yet. Please upload one first.';
                        return;
                    }
                    data.resumes.forEach(resume => {
                        const option = document.createElement('option');
                        option.value = resume.id;
                        option.innerText = resume.filename;
                        resumeSelect.appendChild(option);
                    });
                } else {
                    document.getElementById('message').innerText = 'Failed to load resumes.';
                }
            }

            await loadResumes();
        });

        document.getElementById('parseForm').onsubmit = async function(e) {
            e.preventDefault();
            const resumeId = document.getElementById('resumeSelect').value;
            const token = localStorage.getItem('token');

            if (!resumeId) {
                document.getElementById('message').innerText = 'Please select a resume to parse.';
                return;
            }

            const res = await fetch('/api/parse_resume', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': 'Bearer ' + token
                },
                body: JSON.stringify({ resume_id: parseInt(resumeId) })
            });
            
            const data = await res.json();
            
            if (res.ok) {
                document.getElementById('message').innerText = data.message;
                document.getElementById('parsedDataDisplay').innerText = JSON.stringify(data.parsed_data, null, 2);
            } else {
                document.getElementById('message').innerText = data.detail || 'An error occurred during parsing.';
                document.getElementById('parsedDataDisplay').innerText = '';
            }
        };
    </script>
</body>
</html>