from sqlalchemy import Column, Integer, String, ForeignKey, Text, DateTime
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from .database import Base

class OptimizationHistory(Base):
    __tablename__ = "optimization_history"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    resume_id = Column(Integer, ForeignKey("resumes.id"))
    jd_id = Column(Integer, ForeignKey("job_descriptions.id"), nullable=True)
    suggestions = Column(Text) # JSON string of optimization suggestions
    timestamp = Column(DateTime(timezone=True), server_default=func.now())

    user = relationship("User")
    resume = relationship("Resume")
    job_description = relationship("JobDescription")