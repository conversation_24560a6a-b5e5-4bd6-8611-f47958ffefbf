import os
import PyPDF2
from docx import Document
import re
from werkzeug.utils import secure_filename

class FileProcessor:
    """File processing service for resume and document handling"""
    
    ALLOWED_EXTENSIONS = {'pdf', 'doc', 'docx', 'txt'}
    
    def __init__(self):
        pass
    
    def allowed_file(self, filename):
        """Check if file extension is allowed"""
        return '.' in filename and \
               filename.rsplit('.', 1)[1].lower() in self.ALLOWED_EXTENSIONS
    
    def extract_text(self, file_path):
        """Extract text from uploaded file based on file type"""
        try:
            file_extension = file_path.split('.')[-1].lower()
            
            if file_extension == 'pdf':
                return self._extract_from_pdf(file_path)
            elif file_extension == 'docx':
                return self._extract_from_docx(file_path)
            elif file_extension == 'doc':
                # For .doc files, we'll try to read as text (limited support)
                return self._extract_from_doc(file_path)
            elif file_extension == 'txt':
                return self._extract_from_txt(file_path)
            else:
                raise ValueError(f"Unsupported file type: {file_extension}")
                
        except Exception as e:
            print(f"Error extracting text from {file_path}: {e}")
            return ""
    
    def _extract_from_pdf(self, file_path):
        """Extract text from PDF file"""
        try:
            text = ""
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                for page in pdf_reader.pages:
                    text += page.extract_text() + "\n"
            return self._clean_text(text)
        except Exception as e:
            print(f"Error reading PDF: {e}")
            return ""
    
    def _extract_from_docx(self, file_path):
        """Extract text from DOCX file"""
        try:
            doc = Document(file_path)
            text = ""
            for paragraph in doc.paragraphs:
                text += paragraph.text + "\n"
            return self._clean_text(text)
        except Exception as e:
            print(f"Error reading DOCX: {e}")
            return ""
    
    def _extract_from_doc(self, file_path):
        """Extract text from DOC file (basic support)"""
        try:
            # This is a basic implementation - for production, consider using python-docx2txt
            with open(file_path, 'rb') as file:
                content = file.read()
                # Try to decode as text (this is very basic and may not work well)
                text = content.decode('utf-8', errors='ignore')
                return self._clean_text(text)
        except Exception as e:
            print(f"Error reading DOC: {e}")
            return ""
    
    def _extract_from_txt(self, file_path):
        """Extract text from TXT file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                text = file.read()
            return self._clean_text(text)
        except Exception as e:
            print(f"Error reading TXT: {e}")
            return ""
    
    def _clean_text(self, text):
        """Clean and normalize extracted text"""
        if not text:
            return ""
        
        # Remove extra whitespace and normalize line breaks
        text = re.sub(r'\s+', ' ', text)
        text = re.sub(r'\n+', '\n', text)
        
        # Remove special characters but keep important punctuation
        text = re.sub(r'[^\w\s\.\,\;\:\!\?\-\(\)\[\]\/\@\#\$\%\&\*\+\=]', '', text)
        
        return text.strip()
    
    def get_file_info(self, file_path):
        """Get file information"""
        try:
            stat = os.stat(file_path)
            return {
                'size': stat.st_size,
                'created': stat.st_ctime,
                'modified': stat.st_mtime,
                'extension': file_path.split('.')[-1].lower()
            }
        except Exception as e:
            print(f"Error getting file info: {e}")
            return {}
    
    def validate_file_size(self, file_path, max_size_mb=16):
        """Validate file size"""
        try:
            file_size = os.path.getsize(file_path)
            max_size_bytes = max_size_mb * 1024 * 1024
            return file_size <= max_size_bytes
        except Exception as e:
            print(f"Error validating file size: {e}")
            return False
    
    def delete_file(self, file_path):
        """Safely delete a file"""
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                return True
            return False
        except Exception as e:
            print(f"Error deleting file: {e}")
            return False
