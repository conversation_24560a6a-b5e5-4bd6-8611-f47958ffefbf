#!/usr/bin/env python3
"""
Test script for Dr. Resume AI application
Tests all major functionality to ensure everything works correctly
"""

import requests
import json
import os
import time

# Configuration
API_BASE = 'http://localhost:5000/api'
TEST_USER = {
    'first_name': '<PERSON>',
    'last_name': 'Doe',
    'email': '<EMAIL>',
    'password': 'testpassword123'
}

def test_health_check():
    """Test if the server is running"""
    try:
        response = requests.get(f'{API_BASE}/health')
        if response.status_code == 200:
            print("✅ Server is running")
            return True
        else:
            print("❌ Server health check failed")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to server. Make sure it's running on localhost:5000")
        return False

def test_user_registration():
    """Test user registration"""
    try:
        response = requests.post(f'{API_BASE}/register', json=TEST_USER)
        if response.status_code == 201:
            print("✅ User registration successful")
            return True
        elif response.status_code == 409:
            print("⚠️  User already exists (this is okay for testing)")
            return True
        else:
            print(f"❌ User registration failed: {response.json()}")
            return False
    except Exception as e:
        print(f"❌ User registration error: {e}")
        return False

def test_user_login():
    """Test user login and return token"""
    try:
        login_data = {
            'email': TEST_USER['email'],
            'password': TEST_USER['password']
        }
        response = requests.post(f'{API_BASE}/login', json=login_data)
        if response.status_code == 200:
            data = response.json()
            token = data.get('access_token')
            print("✅ User login successful")
            return token
        else:
            print(f"❌ User login failed: {response.json()}")
            return None
    except Exception as e:
        print(f"❌ User login error: {e}")
        return None

def test_token_verification(token):
    """Test token verification"""
    try:
        headers = {'Authorization': f'Bearer {token}'}
        response = requests.get(f'{API_BASE}/verify_token', headers=headers)
        if response.status_code == 200:
            print("✅ Token verification successful")
            return True
        else:
            print(f"❌ Token verification failed: {response.json()}")
            return False
    except Exception as e:
        print(f"❌ Token verification error: {e}")
        return False

def test_job_description_creation(token):
    """Test job description creation"""
    try:
        headers = {'Authorization': f'Bearer {token}'}
        jd_data = {
            'title': 'Senior Software Engineer',
            'company': 'TechCorp Inc.',
            'description': 'We are looking for a Senior Software Engineer with experience in Python, React, AWS, and machine learning. The ideal candidate should have strong problem-solving skills, leadership experience, and excellent communication abilities.',
            'location': 'San Francisco, CA'
        }
        response = requests.post(f'{API_BASE}/upload_jd', json=jd_data, headers=headers)
        if response.status_code == 201:
            data = response.json()
            jd_id = data['job_description']['id']
            print("✅ Job description creation successful")
            return jd_id
        else:
            print(f"❌ Job description creation failed: {response.json()}")
            return None
    except Exception as e:
        print(f"❌ Job description creation error: {e}")
        return None

def test_dashboard_data(token):
    """Test dashboard data retrieval"""
    try:
        headers = {'Authorization': f'Bearer {token}'}
        response = requests.get(f'{API_BASE}/dashboard', headers=headers)
        if response.status_code == 200:
            data = response.json()
            stats = data.get('stats', {})
            print(f"✅ Dashboard data retrieved successfully")
            print(f"   - Resumes: {stats.get('resume_count', 0)}")
            print(f"   - Job Descriptions: {stats.get('job_description_count', 0)}")
            print(f"   - Analyses: {stats.get('analysis_count', 0)}")
            return True
        else:
            print(f"❌ Dashboard data retrieval failed: {response.json()}")
            return False
    except Exception as e:
        print(f"❌ Dashboard data retrieval error: {e}")
        return False

def test_job_descriptions_list(token):
    """Test job descriptions list retrieval"""
    try:
        headers = {'Authorization': f'Bearer {token}'}
        response = requests.get(f'{API_BASE}/job_descriptions', headers=headers)
        if response.status_code == 200:
            data = response.json()
            jds = data.get('job_descriptions', [])
            print(f"✅ Job descriptions list retrieved ({len(jds)} items)")
            return jds
        else:
            print(f"❌ Job descriptions list retrieval failed: {response.json()}")
            return []
    except Exception as e:
        print(f"❌ Job descriptions list retrieval error: {e}")
        return []

def test_resumes_list(token):
    """Test resumes list retrieval"""
    try:
        headers = {'Authorization': f'Bearer {token}'}
        response = requests.get(f'{API_BASE}/resumes', headers=headers)
        if response.status_code == 200:
            data = response.json()
            resumes = data.get('resumes', [])
            print(f"✅ Resumes list retrieved ({len(resumes)} items)")
            return resumes
        else:
            print(f"❌ Resumes list retrieval failed: {response.json()}")
            return []
    except Exception as e:
        print(f"❌ Resumes list retrieval error: {e}")
        return []

def create_test_resume_file():
    """Create a test resume file"""
    resume_content = """
John Doe
Senior Software Engineer

EXPERIENCE:
- 5+ years of Python development
- React and JavaScript expertise
- AWS cloud services experience
- Machine learning and data analysis
- Team leadership and mentoring
- Agile development methodologies

SKILLS:
- Programming: Python, JavaScript, Java, SQL
- Frameworks: React, Django, Flask, Node.js
- Cloud: AWS, Docker, Kubernetes
- Databases: PostgreSQL, MongoDB, Redis
- Tools: Git, Jenkins, JIRA

EDUCATION:
- Bachelor's Degree in Computer Science
- AWS Certified Solutions Architect
"""
    
    with open('test_resume.txt', 'w') as f:
        f.write(resume_content)
    
    return 'test_resume.txt'

def test_resume_upload(token):
    """Test resume upload"""
    try:
        headers = {'Authorization': f'Bearer {token}'}
        
        # Create test resume file
        resume_file = create_test_resume_file()
        
        with open(resume_file, 'rb') as f:
            files = {'file': f}
            data = {'title': 'John Doe - Senior Software Engineer Resume'}
            response = requests.post(f'{API_BASE}/upload_resume', files=files, data=data, headers=headers)
        
        # Clean up test file
        os.remove(resume_file)
        
        if response.status_code == 201:
            data = response.json()
            resume_id = data['resume']['id']
            print("✅ Resume upload successful")
            return resume_id
        else:
            print(f"❌ Resume upload failed: {response.json()}")
            return None
    except Exception as e:
        print(f"❌ Resume upload error: {e}")
        return None

def test_matching_analysis(token, resume_id, jd_id):
    """Test matching analysis"""
    try:
        headers = {'Authorization': f'Bearer {token}'}
        match_data = {
            'resume_id': resume_id,
            'job_description_id': jd_id
        }
        response = requests.post(f'{API_BASE}/match_score', json=match_data, headers=headers)
        if response.status_code == 200:
            data = response.json()
            score = data['matching_score']
            print(f"✅ Matching analysis successful")
            print(f"   - Overall Score: {score['overall_score']}%")
            print(f"   - Technical Score: {score['technical_score']}%")
            print(f"   - Matched Keywords: {len(score['matched_keywords'])}")
            print(f"   - Missing Keywords: {len(score['missing_keywords'])}")
            return True
        else:
            print(f"❌ Matching analysis failed: {response.json()}")
            return False
    except Exception as e:
        print(f"❌ Matching analysis error: {e}")
        return False

def test_basic_suggestions(token, resume_id, jd_id):
    """Test basic suggestions"""
    try:
        headers = {'Authorization': f'Bearer {token}'}
        suggestion_data = {
            'resume_id': resume_id,
            'job_description_id': jd_id
        }
        response = requests.post(f'{API_BASE}/suggestions', json=suggestion_data, headers=headers)
        if response.status_code == 200:
            data = response.json()
            suggestions = data.get('suggestions', [])
            print(f"✅ Basic suggestions retrieved ({len(suggestions)} suggestions)")
            return True
        else:
            print(f"❌ Basic suggestions failed: {response.json()}")
            return False
    except Exception as e:
        print(f"❌ Basic suggestions error: {e}")
        return False

def run_all_tests():
    """Run all tests"""
    print("🧪 Starting Dr. Resume AI Application Tests\n")
    
    # Test 1: Health Check
    if not test_health_check():
        print("\n❌ Server is not running. Please start the backend server first.")
        return False
    
    # Test 2: User Registration
    if not test_user_registration():
        return False
    
    # Test 3: User Login
    token = test_user_login()
    if not token:
        return False
    
    # Test 4: Token Verification
    if not test_token_verification(token):
        return False
    
    # Test 5: Job Description Creation
    jd_id = test_job_description_creation(token)
    if not jd_id:
        return False
    
    # Test 6: Resume Upload
    resume_id = test_resume_upload(token)
    if not resume_id:
        return False
    
    # Test 7: Dashboard Data
    if not test_dashboard_data(token):
        return False
    
    # Test 8: List Job Descriptions
    jds = test_job_descriptions_list(token)
    
    # Test 9: List Resumes
    resumes = test_resumes_list(token)
    
    # Test 10: Matching Analysis
    if resume_id and jd_id:
        if not test_matching_analysis(token, resume_id, jd_id):
            return False
    
    # Test 11: Basic Suggestions
    if resume_id and jd_id:
        if not test_basic_suggestions(token, resume_id, jd_id):
            return False
    
    print("\n🎉 All tests completed successfully!")
    print("\n📊 Test Summary:")
    print("✅ Server health check")
    print("✅ User registration")
    print("✅ User login")
    print("✅ Token verification")
    print("✅ Job description creation")
    print("✅ Resume upload")
    print("✅ Dashboard data")
    print("✅ Data listing")
    print("✅ Matching analysis")
    print("✅ Basic suggestions")
    
    return True

if __name__ == '__main__':
    success = run_all_tests()
    if success:
        print("\n🚀 Dr. Resume AI is working perfectly!")
        print("You can now access the frontend at: http://localhost:8000")
    else:
        print("\n❌ Some tests failed. Please check the error messages above.")
