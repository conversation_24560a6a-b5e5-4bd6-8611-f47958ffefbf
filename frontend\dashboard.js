// Dashboard JavaScript for Dr. Resume AI
const API_BASE = 'http://localhost:5000/api';

// Global variables
let currentUser = null;
let resumes = [];
let jobDescriptions = [];

// Initialize dashboard
document.addEventListener('DOMContentLoaded', function() {
    checkAuth();
    loadDashboardData();
});

// Authentication check
function checkAuth() {
    const token = localStorage.getItem('token');
    const user = localStorage.getItem('user');
    
    if (!token || !user) {
        window.location.href = 'login.html';
        return;
    }
    
    currentUser = JSON.parse(user);
    document.getElementById('userWelcome').textContent = `Welcome, ${currentUser.first_name}`;
}

// Logout function
function logout() {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    window.location.href = 'index.html';
}

// Load dashboard data
async function loadDashboardData() {
    try {
        await Promise.all([
            loadStats(),
            loadResumes(),
            loadJobDescriptions(),
            loadRecentActivity()
        ]);
    } catch (error) {
        console.error('Error loading dashboard data:', error);
    }
}

// Load user statistics
async function loadStats() {
    try {
        const response = await fetch(`${API_BASE}/dashboard`, {
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
        });
        
        if (response.ok) {
            const data = await response.json();
            document.getElementById('resumeCount').textContent = data.stats.resume_count;
            document.getElementById('jdCount').textContent = data.stats.job_description_count;
            document.getElementById('analysisCount').textContent = data.stats.analysis_count;
            document.getElementById('avgScore').textContent = data.stats.average_match_score + '%';
        }
    } catch (error) {
        console.error('Error loading stats:', error);
    }
}

// Load resumes
async function loadResumes() {
    try {
        const response = await fetch(`${API_BASE}/resumes`, {
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
        });
        
        if (response.ok) {
            const data = await response.json();
            resumes = data.resumes;
            updateResumeSelect();
        }
    } catch (error) {
        console.error('Error loading resumes:', error);
    }
}

// Load job descriptions
async function loadJobDescriptions() {
    try {
        const response = await fetch(`${API_BASE}/job_descriptions`, {
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
        });
        
        if (response.ok) {
            const data = await response.json();
            jobDescriptions = data.job_descriptions;
            updateJDSelect();
        }
    } catch (error) {
        console.error('Error loading job descriptions:', error);
    }
}

// Load recent activity
async function loadRecentActivity() {
    try {
        const response = await fetch(`${API_BASE}/history`, {
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
        });
        
        if (response.ok) {
            const data = await response.json();
            displayRecentActivity(data.history);
        }
    } catch (error) {
        console.error('Error loading recent activity:', error);
    }
}

// Display recent activity
function displayRecentActivity(history) {
    const container = document.getElementById('recentActivity');
    
    if (history.length === 0) {
        container.innerHTML = '<p class="text-gray-500">No recent activity. Start by uploading a resume or adding a job description.</p>';
        return;
    }
    
    const recentItems = history.slice(0, 5);
    container.innerHTML = recentItems.map(item => `
        <div class="border-b border-gray-200 py-3 last:border-b-0">
            <div class="flex justify-between items-start">
                <div>
                    <h4 class="font-medium">${item.resume_title} vs ${item.job_title}</h4>
                    <p class="text-sm text-gray-600">${item.company || 'Unknown Company'}</p>
                    <p class="text-sm text-gray-500">${new Date(item.created_at).toLocaleDateString()}</p>
                </div>
                <div class="text-right">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        item.overall_score >= 70 ? 'bg-green-100 text-green-800' :
                        item.overall_score >= 50 ? 'bg-yellow-100 text-yellow-800' :
                        'bg-red-100 text-red-800'
                    }">
                        ${item.overall_score}% Match
                    </span>
                </div>
            </div>
        </div>
    `).join('');
}

// Show sections
function showUploadResume() {
    hideAllSections();
    document.getElementById('uploadResumeSection').classList.remove('hidden');
}

function showAddJD() {
    hideAllSections();
    document.getElementById('addJDSection').classList.remove('hidden');
}

function showAnalysis() {
    hideAllSections();
    document.getElementById('analysisSection').classList.remove('hidden');
}

function hideAllSections() {
    document.getElementById('defaultContent').classList.add('hidden');
    document.getElementById('uploadResumeSection').classList.add('hidden');
    document.getElementById('addJDSection').classList.add('hidden');
    document.getElementById('analysisSection').classList.add('hidden');
}

// Update select options
function updateResumeSelect() {
    const select = document.getElementById('selectResume');
    select.innerHTML = '<option value="">Choose a resume...</option>';
    
    resumes.forEach(resume => {
        const option = document.createElement('option');
        option.value = resume.id;
        option.textContent = resume.title;
        select.appendChild(option);
    });
}

function updateJDSelect() {
    const select = document.getElementById('selectJD');
    select.innerHTML = '<option value="">Choose a job description...</option>';
    
    jobDescriptions.forEach(jd => {
        const option = document.createElement('option');
        option.value = jd.id;
        option.textContent = `${jd.title} - ${jd.company || 'Unknown Company'}`;
        select.appendChild(option);
    });
}

// Upload resume form handler
document.getElementById('uploadResumeForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const formData = new FormData();
    formData.append('file', document.getElementById('resumeFile').files[0]);
    formData.append('title', document.getElementById('resumeTitle').value);
    
    const messageDiv = document.getElementById('uploadResumeMessage');
    messageDiv.innerHTML = '<div class="text-blue-600">Uploading resume...</div>';
    
    try {
        const response = await fetch(`${API_BASE}/upload_resume`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            },
            body: formData
        });
        
        const data = await response.json();
        
        if (response.ok) {
            messageDiv.innerHTML = '<div class="p-3 bg-green-100 border border-green-400 text-green-700 rounded">Resume uploaded successfully!</div>';
            document.getElementById('uploadResumeForm').reset();
            await loadResumes();
            await loadStats();
        } else {
            messageDiv.innerHTML = '<div class="p-3 bg-red-100 border border-red-400 text-red-700 rounded">' + data.error + '</div>';
        }
    } catch (error) {
        messageDiv.innerHTML = '<div class="p-3 bg-red-100 border border-red-400 text-red-700 rounded">Network error. Please try again.</div>';
    }
});

// Add job description form handler
document.getElementById('addJDForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const formData = {
        title: document.getElementById('jobTitle').value,
        company: document.getElementById('company').value,
        description: document.getElementById('jobDescription').value,
        location: document.getElementById('location').value
    };
    
    const messageDiv = document.getElementById('addJDMessage');
    messageDiv.innerHTML = '<div class="text-blue-600">Adding job description...</div>';
    
    try {
        const response = await fetch(`${API_BASE}/upload_jd`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            },
            body: JSON.stringify(formData)
        });
        
        const data = await response.json();
        
        if (response.ok) {
            messageDiv.innerHTML = '<div class="p-3 bg-green-100 border border-green-400 text-green-700 rounded">Job description added successfully!</div>';
            document.getElementById('addJDForm').reset();
            await loadJobDescriptions();
            await loadStats();
        } else {
            messageDiv.innerHTML = '<div class="p-3 bg-red-100 border border-red-400 text-red-700 rounded">' + data.error + '</div>';
        }
    } catch (error) {
        messageDiv.innerHTML = '<div class="p-3 bg-red-100 border border-red-400 text-red-700 rounded">Network error. Please try again.</div>';
    }
});

// Run analysis
async function runAnalysis() {
    const resumeId = document.getElementById('selectResume').value;
    const jdId = document.getElementById('selectJD').value;
    const resultsDiv = document.getElementById('analysisResults');
    
    if (!resumeId || !jdId) {
        resultsDiv.innerHTML = '<div class="p-3 bg-yellow-100 border border-yellow-400 text-yellow-700 rounded">Please select both a resume and job description.</div>';
        return;
    }
    
    resultsDiv.innerHTML = '<div class="text-blue-600">Calculating match score...</div>';
    
    try {
        const response = await fetch(`${API_BASE}/match_score`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            },
            body: JSON.stringify({
                resume_id: parseInt(resumeId),
                job_description_id: parseInt(jdId)
            })
        });
        
        const data = await response.json();
        
        if (response.ok) {
            displayAnalysisResults(data);
            await loadStats();
            await loadRecentActivity();
        } else {
            resultsDiv.innerHTML = '<div class="p-3 bg-red-100 border border-red-400 text-red-700 rounded">' + data.error + '</div>';
        }
    } catch (error) {
        resultsDiv.innerHTML = '<div class="p-3 bg-red-100 border border-red-400 text-red-700 rounded">Network error. Please try again.</div>';
    }
}

// Display analysis results
function displayAnalysisResults(data) {
    const resultsDiv = document.getElementById('analysisResults');
    const score = data.matching_score;
    
    resultsDiv.innerHTML = `
        <div class="bg-white border rounded-lg p-6">
            <h4 class="text-lg font-semibold mb-4">Analysis Results</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h5 class="font-medium mb-3">Match Scores</h5>
                    <div class="space-y-2">
                        <div class="flex justify-between">
                            <span>Overall Match:</span>
                            <span class="font-semibold ${score.overall_score >= 70 ? 'text-green-600' : score.overall_score >= 50 ? 'text-yellow-600' : 'text-red-600'}">${score.overall_score}%</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Technical Skills:</span>
                            <span class="font-semibold">${score.technical_score}%</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Soft Skills:</span>
                            <span class="font-semibold">${score.soft_skills_score}%</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Experience:</span>
                            <span class="font-semibold">${score.experience_score}%</span>
                        </div>
                    </div>
                </div>
                <div>
                    <h5 class="font-medium mb-3">Keywords Analysis</h5>
                    <div class="space-y-3">
                        <div>
                            <p class="text-sm text-gray-600 mb-1">Matched Keywords (${score.matched_keywords.length})</p>
                            <div class="flex flex-wrap gap-1">
                                ${score.matched_keywords.slice(0, 10).map(keyword => 
                                    `<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">${keyword}</span>`
                                ).join('')}
                                ${score.matched_keywords.length > 10 ? `<span class="text-xs text-gray-500">+${score.matched_keywords.length - 10} more</span>` : ''}
                            </div>
                        </div>
                        <div>
                            <p class="text-sm text-gray-600 mb-1">Missing Keywords (${score.missing_keywords.length})</p>
                            <div class="flex flex-wrap gap-1">
                                ${score.missing_keywords.slice(0, 10).map(keyword => 
                                    `<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">${keyword}</span>`
                                ).join('')}
                                ${score.missing_keywords.length > 10 ? `<span class="text-xs text-gray-500">+${score.missing_keywords.length - 10} more</span>` : ''}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="mt-6">
                <button onclick="getSuggestions(${data.matching_score.id})" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 mr-2">
                    Get Suggestions
                </button>
                <button onclick="getPremiumSuggestions(${data.matching_score.id})" class="bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700">
                    Get Premium Suggestions
                </button>
            </div>
            <div id="suggestionsArea" class="mt-4"></div>
        </div>
    `;
}

// Get basic suggestions
async function getSuggestions(matchingScoreId) {
    const suggestionsArea = document.getElementById('suggestionsArea');
    suggestionsArea.innerHTML = '<div class="text-blue-600">Loading suggestions...</div>';
    
    try {
        const response = await fetch(`${API_BASE}/suggestions`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            },
            body: JSON.stringify({
                resume_id: parseInt(document.getElementById('selectResume').value),
                job_description_id: parseInt(document.getElementById('selectJD').value)
            })
        });
        
        const data = await response.json();
        
        if (response.ok) {
            displaySuggestions(data.suggestions, 'Basic Suggestions');
        } else {
            suggestionsArea.innerHTML = '<div class="p-3 bg-red-100 border border-red-400 text-red-700 rounded">' + data.error + '</div>';
        }
    } catch (error) {
        suggestionsArea.innerHTML = '<div class="p-3 bg-red-100 border border-red-400 text-red-700 rounded">Network error. Please try again.</div>';
    }
}

// Get premium suggestions
async function getPremiumSuggestions(matchingScoreId) {
    const suggestionsArea = document.getElementById('suggestionsArea');
    suggestionsArea.innerHTML = '<div class="text-blue-600">Loading premium suggestions...</div>';
    
    try {
        const response = await fetch(`${API_BASE}/premium_suggestions`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            },
            body: JSON.stringify({
                resume_id: parseInt(document.getElementById('selectResume').value),
                job_description_id: parseInt(document.getElementById('selectJD').value)
            })
        });
        
        const data = await response.json();
        
        if (response.ok) {
            displaySuggestions(data.suggestions, 'Premium AI Suggestions');
        } else {
            suggestionsArea.innerHTML = '<div class="p-3 bg-red-100 border border-red-400 text-red-700 rounded">' + data.error + '</div>';
        }
    } catch (error) {
        suggestionsArea.innerHTML = '<div class="p-3 bg-red-100 border border-red-400 text-red-700 rounded">Network error. Please try again.</div>';
    }
}

// Display suggestions
function displaySuggestions(suggestions, title) {
    const suggestionsArea = document.getElementById('suggestionsArea');
    
    suggestionsArea.innerHTML = `
        <div class="border-t pt-4">
            <h5 class="font-medium mb-3">${title}</h5>
            <div class="space-y-2">
                ${suggestions.map((suggestion, index) => `
                    <div class="p-3 bg-gray-50 rounded-lg">
                        <div class="flex items-start justify-between">
                            <p class="text-sm">${suggestion.suggestion_text}</p>
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                                suggestion.priority === 'high' ? 'bg-red-100 text-red-800' :
                                suggestion.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                                'bg-green-100 text-green-800'
                            }">${suggestion.priority}</span>
                        </div>
                        ${suggestion.category ? `<p class="text-xs text-gray-500 mt-1">Category: ${suggestion.category}</p>` : ''}
                    </div>
                `).join('')}
            </div>
        </div>
    `;
}
