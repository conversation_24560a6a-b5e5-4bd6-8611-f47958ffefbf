<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Upload Job Description - Dr. Resume</title>
</head>
<body>
    <h2>Upload Job Description</h2>
    <form id="uploadJdForm">
        <input type="file" id="jdFile" accept=".pdf,.doc,.docx,.txt" required><br><br>
        <button type="submit">Upload JD</button>
    </form>
    <div id="message"></div>
    <script>
        document.getElementById('uploadJdForm').onsubmit = async function(e) {
            e.preventDefault();
            const jdFile = document.getElementById('jdFile').files[0];
            const token = localStorage.getItem('token');

            if (!jdFile) {
                document.getElementById('message').innerText = 'Please select a file to upload.';
                return;
            }

            if (!token) {
                document.getElementById('message').innerText = 'You must be logged in to upload a job description.';
                return;
            }

            const formData = new FormData();
            formData.append('file', jdFile);

            const res = await fetch('/api/upload_jd', {
                method: 'POST',
                headers: {
                    'Authorization': 'Bearer ' + token
                },
                body: formData
            });
            
            const data = await res.json();
            
            if (res.ok) {
                document.getElementById('message').innerText = data.message;
            } else {
                document.getElementById('message').innerText = data.detail || 'An error occurred during upload.';
            }
        };
    </script>
</body>
</html>