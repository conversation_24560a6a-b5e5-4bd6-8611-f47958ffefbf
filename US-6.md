# US-6: Resume Parsing - <PERSON>. <PERSON>sume AI

This guide explains how to implement resume parsing functionality, which extracts structured data from uploaded resumes. For this initial version, the parsing logic will be a placeholder, simulating extracted data.

---

## 1. Backend Implementation

### a. Parsed Resume Model
- **File:** `backend/models/parsed_resume_model.py`
- **Purpose:** Defines the database table structure to store the extracted, structured data from parsed resumes.
- **Key Steps:**
  1. Creates a `ParsedResume` model with fields for `id`, `resume_id` (foreign key to `Resume`), `user_id` (foreign key to `User`), `text_content` (raw text extracted), and `parsed_data` (JSON string of structured data).
  2. Establishes relationships with `Resume` and `User` models.
- **Code:**
```python
from sqlalchemy import Column, Integer, String, ForeignKey, Text
from sqlalchemy.orm import relationship
from .database import Base

class ParsedResume(Base):
    __tablename__ = "parsed_resumes"

    id = Column(Integer, primary_key=True, index=True)
    resume_id = Column(<PERSON>te<PERSON>, <PERSON><PERSON>ey("resumes.id"), unique=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    text_content = Column(Text) # Extracted raw text
    parsed_data = Column(Text) # JSON string of parsed entities (skills, experience, etc.)

    resume = relationship("Resume")
    owner = relationship("User")
```

### b. Resume Parsing Endpoint
- **File:** `backend/routers/parse.py`
- **Purpose:** Receives a resume ID, simulates parsing its content, and stores the extracted data.
- **Key Steps:**
  1. Requires a valid JWT for authentication.
  2. Validates that the provided `resume_id` belongs to the authenticated user.
  3. Simulates extracting text content and structured data (e.g., name, email, skills, experience).
  4. Stores or updates the parsed data in the `parsed_resumes` table.
- **Code:**
```python
from fastapi import APIRouter, HTTPException, status, Depends, Header
from sqlalchemy.orm import Session
from pydantic import BaseModel
from ..database import SessionLocal
from ..models.user_model import User
from ..models.resume_model import Resume
from ..models.parsed_resume_model import ParsedResume
from .jwt_utils import verify_token
import json

router = APIRouter()

def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

class ParseResumeRequest(BaseModel):
    resume_id: int

@router.post("/parse_resume")
def parse_resume(request: ParseResumeRequest, Authorization: str = Header(None), db: Session = Depends(get_db)):
    if not Authorization or not Authorization.startswith("Bearer "):
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Missing or invalid token")
    token = Authorization.split(" ")[1]
    payload = verify_token(token)
    if not payload:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid token")
    user_id = payload.get("user_id")

    resume = db.query(Resume).filter(Resume.id == request.resume_id, Resume.user_id == user_id).first()
    if not resume:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Resume not found or does not belong to user")

    # Placeholder for actual resume parsing logic
    # In a real application, this would involve calling an NLP service or library
    # For now, we'll simulate some parsed data
    parsed_text_content = f"Extracted text from {resume.filename}: This is a sample resume text content."
    parsed_data_json = {
        "name": "John Doe",
        "email": "<EMAIL>",
        "phone": "************",
        "skills": ["Python", "FastAPI", "SQLAlchemy", "JavaScript", "HTML", "CSS"],
        "experience": [
            {"title": "Software Engineer", "company": "Tech Corp", "years": "2022-Present"},
            {"title": "Junior Developer", "company": "Startup Inc.", "years": "2020-2022"}
        ]
    }
    
    # Check if resume has already been parsed
    existing_parsed_resume = db.query(ParsedResume).filter(ParsedResume.resume_id == resume.id).first()
    if existing_parsed_resume:
        existing_parsed_resume.text_content = parsed_text_content
        existing_parsed_resume.parsed_data = json.dumps(parsed_data_json)
    else:
        new_parsed_resume = ParsedResume(
            resume_id=resume.id,
            user_id=user_id,
            text_content=parsed_text_content,
            parsed_data=json.dumps(parsed_data_json)
        )
        db.add(new_parsed_resume)
    
    db.commit()
    
    return {"success": True, "message": "Resume parsing initiated!", "parsed_data": parsed_data_json}
```

### c. Registering the Router
- **File:** `backend/main.py`
- **Purpose:** Integrates the resume parsing endpoint and the new `ParsedResume` model into the FastAPI application.
- **Code Addition:**
```python
from .models import user_model, resume_model, jd_model, parsed_resume_model
from .routers import auth, login, upload, jd_upload, match, parse

user_model.Base.metadata.create_all(bind=engine)
resume_model.Base.metadata.create_all(bind=engine)
jd_model.Base.metadata.create_all(bind=engine)
parsed_resume_model.Base.metadata.create_all(bind=engine)

app.include_router(parse.router, prefix="/api", tags=["Resume Parsing"])
```

---

## 2. Frontend Implementation

### a. Resume Parsing Page
- **File:** `frontend/parse_resume.html`
- **Purpose:** Allows users to select an uploaded resume and trigger the parsing process, then displays the (simulated) parsed data.
- **Features:**
  - Dropdown menu dynamically populated with the user's uploaded resumes.
  - A button to trigger the parsing process.
  - Displays the parsed data in a readable format.
- **Code:**
```html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Parse Resume - Dr. Resume</title>
</head>
<body>
    <h2>Parse Your Resume</h2>
    <form id="parseForm">
        <label for="resumeSelect">Select Resume to Parse:</label><br>
        <select id="resumeSelect" required>
            <!-- Resumes will be loaded here dynamically -->
        </select><br><br>

        <button type="submit">Parse Resume</button>
    </form>
    <div id="message"></div>
    <div id="parsedResult" style="margin-top: 20px;">
        <h3>Parsed Data:</h3>
        <pre id="parsedDataDisplay"></pre>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', async () => {
            const token = localStorage.getItem('token');
            if (!token) {
                document.getElementById('message').innerText = 'You must be logged in to use this feature.';
                return;
            }

            // Function to fetch and populate resumes
            async function loadResumes() {
                const res = await fetch('/api/resumes', {
                    headers: { 'Authorization': 'Bearer ' + token }
                });
                if (res.ok) {
                    const data = await res.json();
                    const resumeSelect = document.getElementById('resumeSelect');
                    resumeSelect.innerHTML = '';
                    if (data.resumes.length === 0) {
                        document.getElementById('message').innerText = 'No resumes uploaded yet. Please upload one first.';
                        return;
                    }
                    data.resumes.forEach(resume => {
                        const option = document.createElement('option');
                        option.value = resume.id;
                        option.innerText = resume.filename;
                        resumeSelect.appendChild(option);
                    });
                } else {
                    document.getElementById('message').innerText = 'Failed to load resumes.';
                }
            }

            await loadResumes();
        });

        document.getElementById('parseForm').onsubmit = async function(e) {
            e.preventDefault();
            const resumeId = document.getElementById('resumeSelect').value;
            const token = localStorage.getItem('token');

            if (!resumeId) {
                document.getElementById('message').innerText = 'Please select a resume to parse.';
                return;
            }

            const res = await fetch('/api/parse_resume', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': 'Bearer ' + token
                },
                body: JSON.stringify({ resume_id: parseInt(resumeId) })
            });
            
            const data = await res.json();
            
            if (res.ok) {
                document.getElementById('message').innerText = data.message;
                document.getElementById('parsedDataDisplay').innerText = JSON.stringify(data.parsed_data, null, 2);
            } else {
                document.getElementById('message').innerText = data.detail || 'An error occurred during parsing.';
                document.getElementById('parsedDataDisplay').innerText = '';
            }
        };
    </script>
</body>
</html>
```

---

## 3. Integration & Flow
- The user navigates to the `parse_resume.html` page.
- Upon loading, the frontend fetches the user's uploaded resumes to populate the dropdown.
- The user selects a resume and clicks "Parse Resume".
- The frontend sends a POST request to `/api/parse_resume` with the selected resume ID and the user's JWT.
- The backend verifies ownership, performs the (simulated) parsing, and stores the result.
- The frontend displays the parsed data to the user.

---

## 4. How to Build This Feature (Step-by-Step)
1.  **Define the `ParsedResume` model** in `backend/models/parsed_resume_model.py`.
2.  **Update `backend/main.py`** to include `parsed_resume_model` metadata creation and register the `parse` router.
3.  **Create the parsing endpoint** in `backend/routers/parse.py`.
4.  **Build the resume parsing page** in `frontend/parse_resume.html`.
5.  **Test the flow**: Log in, upload a resume, then try to parse it. Verify that the (simulated) parsed data is displayed.

---

## 5. Security Notes
- **Authentication and Authorization:** Ensure only authenticated users can parse their own resumes.
- **Data Storage:** Be mindful of how sensitive parsed data is stored. Encryption at rest might be necessary for production environments.
- **Scalability:** For real-world parsing, consider using dedicated NLP services or libraries that can handle various resume formats and extract data accurately and efficiently.

---

This feature is a crucial step towards enabling advanced AI functionalities like resume optimization and personalized feedback.
