# US-9: History Tracking - Dr. <PERSON>sume AI

This guide explains how to implement a history tracking feature, allowing users to view a log of their activities within the Dr. Resume AI application, such as uploads, parsing, matching, and optimization.

---

## 1. Backend Implementation

### a. History Model
- **File:** `backend/models/history_model.py`
- **Purpose:** Defines the database table structure to store a log of user activities.
- **Key Steps:**
  1. Creates a `History` model with fields for `id`, `user_id` (foreign key to `User`), `activity_type` (e.g., "Resume Upload", "Match"), `description` (a brief text describing the activity), and `timestamp`.
  2. Includes optional foreign keys to link to specific `Resume`, `JobDescription`, `ParsedResume`, `ParsedJobDescription`, and `OptimizationHistory` records.
- **Code:**
```python
from sqlalchemy import Column, Integer, String, ForeignKey, DateTime, Text
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from .database import Base

class History(Base):
    __tablename__ = "history"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    activity_type = Column(String) # e.g., "Resume Upload", "JD Upload", "Resume Parse", "JD Parse", "Match", "Optimize"
    description = Column(Text) # A brief description of the activity
    timestamp = Column(DateTime(timezone=True), server_default=func.now())
    
    # Optional foreign keys to link to specific records
    resume_id = Column(Integer, ForeignKey("resumes.id"), nullable=True)
    jd_id = Column(Integer, ForeignKey("job_descriptions.id"), nullable=True)
    parsed_resume_id = Column(Integer, ForeignKey("parsed_resumes.id"), nullable=True)
    parsed_jd_id = Column(Integer, ForeignKey("parsed_job_descriptions.id"), nullable=True)
    optimization_id = Column(Integer, ForeignKey("optimization_history.id"), nullable=True)

    user = relationship("User")
    resume = relationship("Resume")
    job_description = relationship("JobDescription")
    parsed_resume = relationship("ParsedResume")
    parsed_jd = relationship("ParsedJobDescription")
    optimization_history = relationship("OptimizationHistory")
```

### b. History Endpoint
- **File:** `backend/routers/history.py`
- **Purpose:** Retrieves the activity history for the authenticated user.
- **Key Steps:**
  1. Requires a valid JWT for authentication.
  2. Extracts the `user_id` from the JWT payload.
  3. Queries the `History` table for all records associated with the user, ordered by timestamp (newest first).
  4. Returns the history records.
- **Code:**
```python
from fastapi import APIRouter, HTTPException, status, Depends, Header
from sqlalchemy.orm import Session
from ..database import SessionLocal
from ..models.history_model import History
from .jwt_utils import verify_token

router = APIRouter()

def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

@router.get("/history")
def get_history(Authorization: str = Header(None), db: Session = Depends(get_db)):
    if not Authorization or not Authorization.startswith("Bearer "):
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Missing or invalid token")
    token = Authorization.split(" ")[1]
    payload = verify_token(token)
    if not payload:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid token")
    user_id = payload.get("user_id")

    user_history = db.query(History).filter(History.user_id == user_id).order_by(History.timestamp.desc()).all()
    
    # Convert history objects to dictionaries for JSON serialization
    history_data = []
    for item in user_history:
        history_data.append({
            "id": item.id,
            "activity_type": item.activity_type,
            "description": item.description,
            "timestamp": item.timestamp.isoformat(),
            "resume_id": item.resume_id,
            "jd_id": item.jd_id,
            "parsed_resume_id": item.parsed_resume_id,
            "parsed_jd_id": item.parsed_jd_id,
            "optimization_id": item.optimization_id,
        })

    return {"history": history_data}
```

### c. Registering the Router
- **File:** `backend/main.py`
- **Purpose:** Integrates the history endpoint and the new `History` model into the FastAPI application.
- **Code Addition:**
```python
from .models import user_model, resume_model, jd_model, parsed_resume_model, parsed_jd_model, optimization_model, history_model
from .routers import auth, login, upload, jd_upload, match, parse, parse_jd, optimize, history

user_model.Base.metadata.create_all(bind=engine)
resume_model.Base.metadata.create_all(bind=engine)
jd_model.Base.metadata.create_all(bind=engine)
parsed_resume_model.Base.metadata.create_all(bind=engine)
parsed_jd_model.Base.metadata.create_all(bind=engine)
optimization_model.Base.metadata.create_all(bind=engine)
history_model.Base.metadata.create_all(bind=engine)

app.include_router(history.router, prefix="/api", tags=["History"])
```

### d. Logging Activities (Updates to existing routers)
To make the history tracking functional, you need to add logic to log activities in other routers (e.g., `upload.py`, `jd_upload.py`, `parse.py`, `parse_jd.py`, `match.py`, `optimize.py`).

**Example for `backend/routers/upload.py` (after successful upload):**
```python
from ..models.history_model import History
# ... existing imports ...

@router.post("/upload_resume")
def upload_resume(file: UploadFile = File(...), Authorization: str = Header(None), db: Session = Depends(get_db)):
    # ... existing upload logic ...

    new_resume = Resume(user_id=user_id, file_path=file_location, filename=file.filename)
    db.add(new_resume)
    db.commit()
    db.refresh(new_resume)

    # Log activity
    new_history_entry = History(
        user_id=user_id,
        activity_type="Resume Upload",
        description=f"Uploaded resume: {file.filename}",
        resume_id=new_resume.id
    )
    db.add(new_history_entry)
    db.commit()

    return {"filename": file.filename, "message": "Resume uploaded successfully!"}
```

**You would apply similar logging logic to:**
- `jd_upload.py` (for JD uploads)
- `parse.py` (for resume parsing)
- `parse_jd.py` (for JD parsing)
- `match.py` (for resume-JD matching)
- `optimize.py` (for resume optimization)

---

## 2. Frontend Implementation

### a. Activity History Page
- **File:** `frontend/history.html`
- **Purpose:** Displays a table of the user's past activities.
- **Features:**
  - Fetches activity history from the backend upon page load.
  - Displays activity type, description, and timestamp in a table format.
- **Code:**
```html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Activity History - Dr. Resume</title>
    <style>
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>
    <h2>Your Activity History</h2>
    <div id="message"></div>
    <table id="historyTable">
        <thead>
            <tr>
                <th>Timestamp</th>
                <th>Activity Type</th>
                <th>Description</th>
            </tr>
        </thead>
        <tbody>
            <!-- History items will be loaded here -->
        </tbody>
    </table>

    <script>
        document.addEventListener('DOMContentLoaded', async () => {
            const token = localStorage.getItem('token');
            if (!token) {
                document.getElementById('message').innerText = 'You must be logged in to view your history.';
                return;
            }

            const res = await fetch('/api/history', {
                headers: { 'Authorization': 'Bearer ' + token }
            });
            
            const data = await res.json();
            const historyTableBody = document.querySelector('#historyTable tbody');
            
            if (res.ok && data.history && data.history.length > 0) {
                data.history.forEach(item => {
                    const row = historyTableBody.insertRow();
                    row.insertCell().innerText = new Date(item.timestamp).toLocaleString();
                    row.insertCell().innerText = item.activity_type;
                    row.insertCell().innerText = item.description;
                });
            } else if (res.ok && data.history.length === 0) {
                document.getElementById('message').innerText = 'No activity history found.';
            } else {
                document.getElementById('message').innerText = data.detail || 'Failed to load history.';
            }
        });
    </script>
</body>
</html>
```

---

## 3. Integration & Flow
- The user navigates to the `history.html` page.
- Upon loading, the frontend sends a GET request to `/api/history` with the user's JWT.
- The backend retrieves all history records for that user.
- The frontend displays these records in a formatted table.
- **Crucially, for this feature to work, you need to go back to the previous backend endpoints (upload, parse, match, optimize) and add code to create `History` entries after successful operations.**

---

## 4. How to Build This Feature (Step-by-Step)
1.  **Define the `History` model** in `backend/models/history_model.py`.
2.  **Update `backend/main.py`** to include `history_model` metadata creation and register the `history` router.
3.  **Create the history retrieval endpoint** in `backend/routers/history.py`.
4.  **Build the activity history page** in `frontend/history.html`.
5.  **IMPORTANT: Modify existing backend routers** (`upload.py`, `jd_upload.py`, `parse.py`, `parse_jd.py`, `match.py`, `optimize.py`) to **add `History` entries** after successful operations. This is critical for populating the history.
6.  **Test the flow**: Log in, perform various actions (upload resume/JD, parse, match, optimize), then navigate to the history page to verify that all activities are logged correctly.

---

## 5. Security Notes
- **Authentication and Authorization:** Ensure that users can only view their own activity history.
- **Data Granularity:** Decide what level of detail to store in the history. Avoid storing sensitive information directly in the `description` field; instead, link to the relevant record via foreign keys.
- **Data Retention:** Consider a data retention policy for history records in a production environment to manage database size.

---

This feature provides users with valuable insights into their past interactions with the Dr. Resume AI application.
