# US-5: Resume-JD Matching/Comparison - Dr. Resume AI

This guide explains how to implement the basic framework for matching resumes with job descriptions. For this initial version, the matching logic will be a placeholder, focusing on setting up the API endpoint and frontend interaction. The actual AI/NLP processing will be integrated in later user stories.

---

## 1. Backend Implementation

### a. Matching Endpoint
- **File:** `backend/routers/match.py`
- **Purpose:** Receives selected resume and JD IDs, performs a placeholder matching operation, and returns a score and feedback.
- **Key Steps:**
  1. Requires a valid JWT for authentication.
  2. Validates that the provided `resume_id` and `jd_id` belong to the authenticated user.
  3. Returns a hardcoded match score and feedback as a placeholder for future AI integration.
- **Code:**
```python
from fastapi import APIRouter, HTTPException, status, Depends, Header
from sqlalchemy.orm import Session
from pydantic import BaseModel
from ..database import SessionLocal
from ..models.user_model import User
from ..models.resume_model import Resume
from ..models.jd_model import JobDescription
from .jwt_utils import verify_token

router = APIRouter()

def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

class MatchRequest(BaseModel):
    resume_id: int
    jd_id: int

@router.post("/match_resume_jd")
def match_resume_jd(request: MatchRequest, Authorization: str = Header(None), db: Session = Depends(get_db)):
    if not Authorization or not Authorization.startswith("Bearer "):
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Missing or invalid token")
    token = Authorization.split(" ")[1]
    payload = verify_token(token)
    if not payload:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid token")
    user_id = payload.get("user_id")

    # Verify that the resume and JD belong to the authenticated user
    resume = db.query(Resume).filter(Resume.id == request.resume_id, Resume.user_id == user_id).first()
    jd = db.query(JobDescription).filter(JobDescription.id == request.jd_id, JobDescription.user_id == user_id).first()

    if not resume:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Resume not found or does not belong to user")
    if not jd:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Job Description not found or does not belong to user")

    # Placeholder for actual matching logic
    # In a real application, this would involve AI/NLP processing
    match_score = "75%"
    feedback = "This is a placeholder feedback. Implement actual AI matching here."

    return {"success": True, "message": "Matching initiated successfully!", "match_score": match_score, "feedback": feedback}
```

### b. Registering the Router
- **File:** `backend/main.py`
- **Purpose:** Integrates the matching endpoint into the FastAPI application.
- **Code Addition:**
```python
from .routers import match

app.include_router(match.router, prefix="/api", tags=["Matching"])
```

### c. Listing Resumes and JDs (for Frontend Dropdowns)
- **File:** `backend/routers/upload.py` (for resumes) and `backend/routers/jd_upload.py` (for JDs)
- **Purpose:** Provides endpoints for the frontend to fetch a list of uploaded resumes and JDs for the dropdown menus.
- **Code Addition to `backend/routers/upload.py`:**
```python
@router.get("/resumes")
def list_resumes(Authorization: str = Header(None), db: Session = Depends(get_db)):
    if not Authorization or not Authorization.startswith("Bearer "):
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Missing or invalid token")
    token = Authorization.split(" ")[1]
    payload = verify_token(token)
    if not payload:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid token")
    user_id = payload.get("user_id")

    resumes = db.query(Resume).filter(Resume.user_id == user_id).all()
    return {"resumes": resumes}
```
- **Code Addition to `backend/routers/jd_upload.py`:**
```python
@router.get("/jds")
def list_jds(Authorization: str = Header(None), db: Session = Depends(get_db)):
    if not Authorization or not Authorization.startswith("Bearer "):
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Missing or invalid token")
    token = Authorization.split(" ")[1]
    payload = verify_token(token)
    if not payload:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid token")
    user_id = payload.get("user_id")

    jds = db.query(JobDescription).filter(JobDescription.user_id == user_id).all()
    return {"jds": jds}
```

---

## 2. Frontend Implementation

### a. Matching Page
- **File:** `frontend/match_resume_jd.html`
- **Purpose:** Allows users to select an uploaded resume and job description, then initiate the matching process.
- **Features:**
  - Dropdown menus dynamically populated with the user's uploaded resumes and JDs.
  - A button to trigger the matching process.
  - Displays the match score and feedback received from the backend.
- **Code:**
```html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Resume-JD Match - Dr. Resume</title>
</head>
<body>
    <h2>Match Resume with Job Description</h2>
    <form id="matchForm">
        <label for="resumeSelect">Select Resume:</label><br>
        <select id="resumeSelect" required>
            <!-- Resumes will be loaded here dynamically -->
        </select><br><br>

        <label for="jdSelect">Select Job Description:</label><br>
        <select id="jdSelect" required>
            <!-- JDs will be loaded here dynamically -->
        </select><br><br>

        <button type="submit">Get Match Score</button>
    </form>
    <div id="message"></div>
    <div id="matchResult" style="margin-top: 20px;">
        <h3>Match Result:</h3>
        <p><strong>Score:</strong> <span id="score"></span></p>
        <p><strong>Feedback:</strong> <span id="feedback"></span></p>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', async () => {
            const token = localStorage.getItem('token');
            if (!token) {
                document.getElementById('message').innerText = 'You must be logged in to use this feature.';
                return;
            }

            // Function to fetch and populate resumes
            async function loadResumes() {
                const res = await fetch('/api/resumes', {
                    headers: { 'Authorization': 'Bearer ' + token }
                });
                if (res.ok) {
                    const data = await res.json();
                    const resumeSelect = document.getElementById('resumeSelect');
                    resumeSelect.innerHTML = '';
                    data.resumes.forEach(resume => {
                        const option = document.createElement('option');
                        option.value = resume.id;
                        option.innerText = resume.filename;
                        resumeSelect.appendChild(option);
                    });
                } else {
                    document.getElementById('message').innerText = 'Failed to load resumes.';
                }
            }

            // Function to fetch and populate JDs
            async function loadJDs() {
                const res = await fetch('/api/jds', {
                    headers: { 'Authorization': 'Bearer ' + token }
                });
                if (res.ok) {
                    const data = await res.json();
                    const jdSelect = document.getElementById('jdSelect');
                    jdSelect.innerHTML = '';
                    data.jds.forEach(jd => {
                        const option = document.createElement('option');
                        option.value = jd.id;
                        option.innerText = jd.filename;
                        jdSelect.appendChild(option);
                    });
                } else {
                    document.getElementById('message').innerText = 'Failed to load job descriptions.';
                }
            }

            await loadResumes();
            await loadJDs();
        });

        document.getElementById('matchForm').onsubmit = async function(e) {
            e.preventDefault();
            const resumeId = document.getElementById('resumeSelect').value;
            const jdId = document.getElementById('jdSelect').value;
            const token = localStorage.getItem('token');

            if (!resumeId || !jdId) {
                document.getElementById('message').innerText = 'Please select both a resume and a job description.';
                return;
            }

            const res = await fetch('/api/match_resume_jd', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': 'Bearer ' + token
                },
                body: JSON.stringify({ resume_id: parseInt(resumeId), jd_id: parseInt(jdId) })
            });
            
            const data = await res.json();
            
            if (res.ok) {
                document.getElementById('message').innerText = data.message;
                document.getElementById('score').innerText = data.match_score;
                document.getElementById('feedback').innerText = data.feedback;
            } else {
                document.getElementById('message').innerText = data.detail || 'An error occurred during matching.';
                document.getElementById('score').innerText = '';
                document.getElementById('feedback').innerText = '';
            }
        };
    </script>
</body>
</html>
```

---

## 3. Integration & Flow
- The user navigates to the `match_resume_jd.html` page.
- Upon loading, the frontend fetches the user's uploaded resumes and JDs to populate the dropdowns.
- The user selects a resume and a JD, then clicks "Get Match Score".
- The frontend sends a POST request to `/api/match_resume_jd` with the selected IDs and the user's JWT.
- The backend verifies ownership, performs the (placeholder) matching, and returns a score and feedback.
- The frontend displays the results to the user.

---

## 4. How to Build This Feature (Step-by-Step)
1. **Create the matching endpoint** in `backend/routers/match.py`.
2. **Register the `match` router** in `backend/main.py`.
3. **Add list endpoints for resumes and JDs** to `backend/routers/upload.py` and `backend/routers/jd_upload.py` respectively.
4. **Build the matching page** in `frontend/match_resume_jd.html`.
5. **Test the flow**: Log in, upload a resume and a JD, then try to get a match score. Verify that the dropdowns are populated and the placeholder result is displayed.

---

## 5. Security Notes
- **Authentication and Authorization:** Crucially, the backend verifies that the selected resume and JD belong to the authenticated user to prevent unauthorized access to other users' documents.
- **Placeholder Logic:** Remember that the current matching logic is a placeholder. In a real application, this would involve secure integration with an AI/NLP service, ensuring data privacy and handling potential large language model (LLM) costs.

---

This feature lays the groundwork for the core AI-powered matching capability of Dr. Resume AI.
