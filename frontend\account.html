<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Account Settings - Dr. Resume</title>
</head>
<body>
    <h2>Account Settings</h2>
    <form id="accountForm">
        <label>New Email:</label><br>
        <input type="email" id="email"><br>
        <label>New Password:</label><br>
        <input type="password" id="password"><br>
        <label>Current Password (required):</label><br>
        <input type="password" id="current_password" required><br><br>
        <button type="submit">Save/Update</button>
    </form>
    <div id="message"></div>
    <script>
        document.getElementById('accountForm').onsubmit = async function(e) {
            e.preventDefault();
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const current_password = document.getElementById('current_password').value;
            const token = localStorage.getItem('token');

            if (!token) {
                document.getElementById('message').innerText = 'You must be logged in to update account settings.';
                return;
            }

            const body = {};
            if (email) body.email = email;
            if (password) body.password = password;
            body.current_password = current_password;

            const res = await fetch('/api/update_account', {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': 'Bearer ' + token
                },
                body: JSON.stringify(body)
            });
            const data = await res.json();
            document.getElementById('message').innerText = data.message || (data.success ? 'Updated!' : 'Error');
        };
    </script>
</body>
</html>