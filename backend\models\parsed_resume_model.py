from sqlalchemy import Column, Integer, String, ForeignKey, Text
from sqlalchemy.orm import relationship
from .database import Base

class ParsedResume(Base):
    __tablename__ = "parsed_resumes"

    id = Column(Integer, primary_key=True, index=True)
    resume_id = Column(Integer, ForeignKey("resumes.id"), unique=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    text_content = Column(Text) # Extracted raw text
    parsed_data = Column(Text) # JSON string of parsed entities (skills, experience, etc.)

    resume = relationship("Resume")
    owner = relationship("User")