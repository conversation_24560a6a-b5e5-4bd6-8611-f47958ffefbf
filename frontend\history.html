<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Activity History - Dr. Resume</title>
    <style>
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>
    <h2>Your Activity History</h2>
    <div id="message"></div>
    <table id="historyTable">
        <thead>
            <tr>
                <th>Timestamp</th>
                <th>Activity Type</th>
                <th>Description</th>
            </tr>
        </thead>
        <tbody>
            <!-- History items will be loaded here -->
        </tbody>
    </table>

    <script>
        document.addEventListener('DOMContentLoaded', async () => {
            const token = localStorage.getItem('token');
            if (!token) {
                document.getElementById('message').innerText = 'You must be logged in to view your history.';
                return;
            }

            const res = await fetch('/api/history', {
                headers: { 'Authorization': 'Bearer ' + token }
            });
            
            const data = await res.json();
            const historyTableBody = document.querySelector('#historyTable tbody');
            
            if (res.ok && data.history && data.history.length > 0) {
                data.history.forEach(item => {
                    const row = historyTableBody.insertRow();
                    row.insertCell().innerText = new Date(item.timestamp).toLocaleString();
                    row.insertCell().innerText = item.activity_type;
                    row.insertCell().innerText = item.description;
                });
            } else if (res.ok && data.history.length === 0) {
                document.getElementById('message').innerText = 'No activity history found.';
            } else {
                document.getElementById('message').innerText = data.detail || 'Failed to load history.';
            }
        });
    </script>
</body>
</html>