# US-10: Account Settings - <PERSON>. <PERSON>sume AI

This guide explains how to implement account settings for updating email and password, with re-authentication, using FastAPI and a minimal HTML frontend.

---

## 1. Backend Implementation

### a. Account Update Endpoint
- **File:** `backend/routers/account.py`
- **Purpose:** Allows users to update their email and/or password after verifying their current password.
- **Key Steps:**
  1. Checks for a valid JWT token in the Authorization header.
  2. Verifies the user's current password.
  3. Updates email and/or password if provided.
  4. Returns a success or error message.
- **Code:**
```python
from fastapi import APIRouter, HTTPException, status, Depends, Header
from sqlalchemy.orm import Session
from pydantic import BaseModel, EmailStr
from passlib.context import CryptContext
from ..database import SessionLocal
from ..models.user_model import User
from .jwt_utils import verify_token

router = APIRouter()
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

class UpdateAccountRequest(BaseModel):
    email: EmailStr | None = None
    password: str | None = None
    current_password: str

@router.put("/update_account")
def update_account(request: UpdateAccountRequest, Authorization: str = Header(None), db: Session = Depends(get_db)):
    if not Authorization or not Authorization.startswith("Bearer "):
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Missing or invalid token")
    token = Authorization.split(" ")[1]
    payload = verify_token(token)
    if not payload:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid token")
    user_id = payload.get("user_id")

    user = db.query(User).filter(User.id == user_id).first()
    if not user or not pwd_context.verify(request.current_password, user.hashed_password):
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Current password incorrect")

    updated = False
    if request.email and request.email != user.email:
        user.email = request.email
        updated = True
    if request.password:
        user.hashed_password = pwd_context.hash(request.password)
        updated = True

    if updated:
        db.commit()
        return {"success": True, "message": "Account updated successfully"}
    return {"success": False, "message": "No changes made"}
```

### b. Registering the Router
- **File:** `backend/main.py`
- **Purpose:** Integrates the account update endpoint into the FastAPI app.
- **Code Addition:**
```python
from .routers import account

app.include_router(account.router, prefix="/api", tags=["Account Settings"])
```

---

## 2. Frontend Implementation

### a. Account Settings Page
- **File:** `frontend/account.html`
- **Purpose:** Allows users to update their email and/or password after entering their current password.
- **Features:**
  - Email and password input fields
  - Current password required
  - Save/Update button
  - Shows confirmation or error message
- **Code:**
```html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Account Settings - Dr. Resume</title>
</head>
<body>
    <h2>Account Settings</h2>
    <form id="accountForm">
        <label>New Email:</label><br>
        <input type="email" id="email"><br>
        <label>New Password:</label><br>
        <input type="password" id="password"><br>
        <label>Current Password (required):</label><br>
        <input type="password" id="current_password" required><br><br>
        <button type="submit">Save/Update</button>
    </form>
    <div id="message"></div>
    <script>
        document.getElementById('accountForm').onsubmit = async function(e) {
            e.preventDefault();
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const current_password = document.getElementById('current_password').value;
            const token = localStorage.getItem('token');

            if (!token) {
                document.getElementById('message').innerText = 'You must be logged in to update account settings.';
                return;
            }

            const body = {};
            if (email) body.email = email;
            if (password) body.password = password;
            body.current_password = current_password;

            const res = await fetch('/api/update_account', {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': 'Bearer ' + token
                },
                body: JSON.stringify(body)
            });
            const data = await res.json();
            document.getElementById('message').innerText = data.message || (data.success ? 'Updated!' : 'Error');
        };
    </script>
</body>
</html>
```

---

## 3. Integration & Flow
- The user navigates to the `account.html` page.
- They can optionally enter a new email and/or password, but must provide their current password for verification.
- The frontend sends a PUT request to `/api/update_account` with the new email/password (if provided) and the current password, along with the user's JWT.
- The backend verifies the current password and the JWT, then updates the user's email and/or hashed password in the database.
- The frontend displays the result to the user.

---

## 4. How to Build This Feature (Step-by-Step)
1.  **Create the account update endpoint** in `backend/routers/account.py`.
2.  **Register the `account` router** in `backend/main.py`.
3.  **Build the account settings page** in `frontend/account.html`.
4.  **Test the flow**: Log in, try updating your email and/or password, and verify the changes. Test with correct and incorrect current passwords.

---

## 5. Security Notes
- **Re-authentication:** Requiring the current password for any account changes (especially password changes) is a critical security measure to prevent unauthorized modifications if a user's session token is compromised.
- **JWT Token:** All requests to update account settings must be authenticated with a valid JWT.
- **Password Hashing:** Passwords are always stored as bcrypt hashes, never in plaintext.
- **Input Validation:** Ensure proper validation of email format and password strength (though not explicitly shown in this minimal example, it's crucial for production).

---

This feature provides users with the ability to manage their account credentials securely.
