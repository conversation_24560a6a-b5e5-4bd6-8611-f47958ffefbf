from fastapi import APIRouter, HTTPException, status, Depends, Header
from sqlalchemy.orm import Session
from pydantic import BaseModel
from ..database import SessionLocal
from ..models.user_model import User
from ..models.resume_model import Resume
from ..models.parsed_resume_model import ParsedResume
from .jwt_utils import verify_token
import json

router = APIRouter()

def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

class ParseResumeRequest(BaseModel):
    resume_id: int

@router.post("/parse_resume")
def parse_resume(request: ParseResumeRequest, Authorization: str = Header(None), db: Session = Depends(get_db)):
    if not Authorization or not Authorization.startswith("Bearer "):
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Missing or invalid token")
    token = Authorization.split(" ")[1]
    payload = verify_token(token)
    if not payload:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid token")
    user_id = payload.get("user_id")

    resume = db.query(Resume).filter(Resume.id == request.resume_id, Resume.user_id == user_id).first()
    if not resume:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Resume not found or does not belong to user")

    # Placeholder for actual resume parsing logic
    # In a real application, this would involve calling an NLP service or library
    # For now, we'll simulate some parsed data
    parsed_text_content = f"Extracted text from {resume.filename}: This is a sample resume text content."
    parsed_data_json = {
        "name": "John Doe",
        "email": "<EMAIL>",
        "phone": "************",
        "skills": ["Python", "FastAPI", "SQLAlchemy", "JavaScript", "HTML", "CSS"],
        "experience": [
            {"title": "Software Engineer", "company": "Tech Corp", "years": "2022-Present"},
            {"title": "Junior Developer", "company": "Startup Inc.", "years": "2020-2022"}
        ]
    }
    
    from ..models.history_model import History
    # Check if resume has already been parsed
    existing_parsed_resume = db.query(ParsedResume).filter(ParsedResume.resume_id == resume.id).first()
    if existing_parsed_resume:
        existing_parsed_resume.text_content = parsed_text_content
        existing_parsed_resume.parsed_data = json.dumps(parsed_data_json)
        db.commit()
        db.refresh(existing_parsed_resume)
        parsed_resume_id = existing_parsed_resume.id
    else:
        new_parsed_resume = ParsedResume(
            resume_id=resume.id,
            user_id=user_id,
            text_content=parsed_text_content,
            parsed_data=json.dumps(parsed_data_json)
        )
        db.add(new_parsed_resume)
        db.commit()
        db.refresh(new_parsed_resume)
        parsed_resume_id = new_parsed_resume.id
    
    # Log activity
    new_history_entry = History(
        user_id=user_id,
        activity_type="Resume Parse",
        description=f"Parsed resume: {resume.filename}",
        resume_id=resume.id,
        parsed_resume_id=parsed_resume_id
    )
    db.add(new_history_entry)
    db.commit()

    return {"success": True, "message": "Resume parsing initiated!", "parsed_data": parsed_data_json}