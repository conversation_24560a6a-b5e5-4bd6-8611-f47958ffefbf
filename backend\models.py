from flask_sqlalchemy import SQLAlchemy
from datetime import datetime
from werkzeug.security import generate_password_hash, check_password_hash

db = SQLAlchemy()

# US-01: User Registration Model
class User(db.Model):
    __tablename__ = 'users'

    id = db.Column(db.Integer, primary_key=True)
    first_name = db.Column(db.String(50), nullable=False)
    last_name = db.Column(db.String(50), nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False, index=True)
    password_hash = db.Column(db.String(255), nullable=False)
    is_premium = db.Column(db.<PERSON><PERSON><PERSON>, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    resumes = db.relationship('Resume', backref='user', lazy=True, cascade='all, delete-orphan')
    job_descriptions = db.relationship('JobDescription', backref='user', lazy=True, cascade='all, delete-orphan')
    matching_scores = db.relationship('MatchingScore', backref='user', lazy=True, cascade='all, delete-orphan')
    suggestions = db.relationship('Suggestion', backref='user', lazy=True, cascade='all, delete-orphan')
    analytics = db.relationship('Analytics', backref='user', lazy=True, cascade='all, delete-orphan')

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

    def to_dict(self):
        return {
            'id': self.id,
            'first_name': self.first_name,
            'last_name': self.last_name,
            'email': self.email,
            'is_premium': self.is_premium,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

# US-03: Resume Upload Model
class Resume(db.Model):
    __tablename__ = 'resumes'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    title = db.Column(db.String(200), nullable=False)
    filename = db.Column(db.String(255), nullable=False)
    file_path = db.Column(db.String(500), nullable=False)
    file_size = db.Column(db.Integer)
    file_type = db.Column(db.String(50))
    extracted_text = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    keywords = db.relationship('Keyword', backref='resume', lazy=True, cascade='all, delete-orphan')
    matching_scores = db.relationship('MatchingScore', backref='resume', lazy=True, cascade='all, delete-orphan')

    def to_dict(self):
        return {
            'id': self.id,
            'title': self.title,
            'filename': self.filename,
            'file_size': self.file_size,
            'file_type': self.file_type,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'has_text': bool(self.extracted_text)
        }

# US-04: Job Description Model
class JobDescription(db.Model):
    __tablename__ = 'job_descriptions'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    title = db.Column(db.String(200), nullable=False)
    company = db.Column(db.String(200))
    description = db.Column(db.Text, nullable=False)
    location = db.Column(db.String(200))
    salary_range = db.Column(db.String(100))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    keywords = db.relationship('Keyword', backref='job_description', lazy=True, cascade='all, delete-orphan')
    matching_scores = db.relationship('MatchingScore', backref='job_description', lazy=True, cascade='all, delete-orphan')

    def to_dict(self):
        return {
            'id': self.id,
            'title': self.title,
            'company': self.company,
            'description': self.description[:200] + '...' if len(self.description) > 200 else self.description,
            'location': self.location,
            'salary_range': self.salary_range,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

# US-05: Keyword Extraction Model
class Keyword(db.Model):
    __tablename__ = 'keywords'

    id = db.Column(db.Integer, primary_key=True)
    resume_id = db.Column(db.Integer, db.ForeignKey('resumes.id'), nullable=True)
    job_description_id = db.Column(db.Integer, db.ForeignKey('job_descriptions.id'), nullable=True)
    keyword = db.Column(db.String(100), nullable=False)
    category = db.Column(db.String(50))  # technical, soft_skill, experience, education
    frequency = db.Column(db.Integer, default=1)
    confidence_score = db.Column(db.Float, default=1.0)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def to_dict(self):
        return {
            'id': self.id,
            'keyword': self.keyword,
            'category': self.category,
            'frequency': self.frequency,
            'confidence_score': self.confidence_score
        }

# US-06: Matching Score Model
class MatchingScore(db.Model):
    __tablename__ = 'matching_scores'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    resume_id = db.Column(db.Integer, db.ForeignKey('resumes.id'), nullable=False)
    job_description_id = db.Column(db.Integer, db.ForeignKey('job_descriptions.id'), nullable=False)
    overall_score = db.Column(db.Float, nullable=False)  # 0.0 to 1.0
    technical_score = db.Column(db.Float, default=0.0)
    soft_skills_score = db.Column(db.Float, default=0.0)
    experience_score = db.Column(db.Float, default=0.0)
    matched_keywords = db.Column(db.JSON)  # List of matched keywords
    missing_keywords = db.Column(db.JSON)  # List of missing keywords
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def to_dict(self):
        return {
            'id': self.id,
            'overall_score': round(self.overall_score * 100, 2),
            'technical_score': round(self.technical_score * 100, 2),
            'soft_skills_score': round(self.soft_skills_score * 100, 2),
            'experience_score': round(self.experience_score * 100, 2),
            'matched_keywords': self.matched_keywords or [],
            'missing_keywords': self.missing_keywords or [],
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

# US-07: Suggestions Model
class Suggestion(db.Model):
    __tablename__ = 'suggestions'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    resume_id = db.Column(db.Integer, db.ForeignKey('resumes.id'), nullable=False)
    job_description_id = db.Column(db.Integer, db.ForeignKey('job_descriptions.id'), nullable=False)
    suggestion_type = db.Column(db.String(50), nullable=False)  # basic, premium
    suggestion_text = db.Column(db.Text, nullable=False)
    category = db.Column(db.String(50))  # skills, experience, format, keywords
    priority = db.Column(db.String(20), default='medium')  # low, medium, high
    is_implemented = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def to_dict(self):
        return {
            'id': self.id,
            'suggestion_type': self.suggestion_type,
            'suggestion_text': self.suggestion_text,
            'category': self.category,
            'priority': self.priority,
            'is_implemented': self.is_implemented,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

# US-08: Analytics Model
class Analytics(db.Model):
    __tablename__ = 'analytics'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    action_type = db.Column(db.String(50), nullable=False)  # upload_resume, upload_jd, match_analysis, view_suggestions
    entity_id = db.Column(db.Integer)  # ID of the related entity (resume, jd, etc.)
    entity_type = db.Column(db.String(50))  # resume, job_description, matching_score
    metadata = db.Column(db.JSON)  # Additional data about the action
    ip_address = db.Column(db.String(45))
    user_agent = db.Column(db.String(500))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def to_dict(self):
        return {
            'id': self.id,
            'action_type': self.action_type,
            'entity_type': self.entity_type,
            'metadata': self.metadata or {},
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

# Database initialization helper
def init_db(app):
    """Initialize database with all tables"""
    with app.app_context():
        db.create_all()
        print("Database tables created successfully!")

def get_user_stats(user_id):
    """Get user statistics for dashboard"""
    resume_count = Resume.query.filter_by(user_id=user_id).count()
    jd_count = JobDescription.query.filter_by(user_id=user_id).count()
    match_count = MatchingScore.query.filter_by(user_id=user_id).count()

    # Calculate average match score
    avg_score = db.session.query(db.func.avg(MatchingScore.overall_score)).filter_by(user_id=user_id).scalar()
    avg_score = round(avg_score * 100, 2) if avg_score else 0

    return {
        'resume_count': resume_count,
        'job_description_count': jd_count,
        'analysis_count': match_count,
        'average_match_score': avg_score
    }
