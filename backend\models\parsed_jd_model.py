from sqlalchemy import Column, Integer, String, ForeignKey, Text
from sqlalchemy.orm import relationship
from .database import Base

class ParsedJobDescription(Base):
    __tablename__ = "parsed_job_descriptions"

    id = Column(Integer, primary_key=True, index=True)
    jd_id = Column(Integer, ForeignKey("job_descriptions.id"), unique=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    text_content = Column(Text) # Extracted raw text
    parsed_data = Column(Text) # JSON string of parsed entities (skills, requirements, etc.)

    job_description = relationship("JobDescription")
    owner = relationship("User")