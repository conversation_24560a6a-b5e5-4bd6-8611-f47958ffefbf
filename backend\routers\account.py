from fastapi import APIRouter, HTTPException, status, Depends, Header
from sqlalchemy.orm import Session
from pydantic import BaseModel, EmailStr
from passlib.context import CryptContext
from ..database import SessionLocal
from ..models.user_model import User
from .jwt_utils import verify_token

router = APIRouter()
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

class UpdateAccountRequest(BaseModel):
    email: EmailStr | None = None
    password: str | None = None
    current_password: str

@router.put("/update_account")
def update_account(request: UpdateAccountRequest, Authorization: str = Header(None), db: Session = Depends(get_db)):
    if not Authorization or not Authorization.startswith("Bearer "):
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Missing or invalid token")
    token = Authorization.split(" ")[1]
    payload = verify_token(token)
    if not payload:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid token")
    user_id = payload.get("user_id")

    user = db.query(User).filter(User.id == user_id).first()
    if not user or not pwd_context.verify(request.current_password, user.hashed_password):
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Current password incorrect")

    updated = False
    if request.email and request.email != user.email:
        user.email = request.email
        updated = True
    if request.password:
        user.hashed_password = pwd_context.hash(request.password)
        updated = True

    if updated:
        db.commit()
        return {"success": True, "message": "Account updated successfully"}
    return {"success": False, "message": "No changes made"}