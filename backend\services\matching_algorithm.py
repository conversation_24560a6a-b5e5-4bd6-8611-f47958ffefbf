from typing import Dict, List, Set
from models import db, Resume, JobDescription, Keyword

class MatchingAlgorithm:
    """Resume-Job Description matching algorithm using Jaccard similarity"""
    
    def __init__(self):
        self.category_weights = {
            'technical_programming': 0.25,
            'technical_web': 0.20,
            'technical_database': 0.15,
            'technical_cloud': 0.15,
            'technical_tools': 0.10,
            'technical_frameworks': 0.10,
            'soft_skill': 0.20,
            'experience_junior': 0.05,
            'experience_mid': 0.10,
            'experience_senior': 0.15,
            'experience_executive': 0.20,
            'education': 0.10,
            'general': 0.05
        }
    
    def calculate_match(self, resume_id: int, job_description_id: int) -> Dict:
        """Calculate matching score between resume and job description"""
        try:
            # Get keywords for resume and job description
            resume_keywords = self._get_keywords(resume_id, 'resume')
            jd_keywords = self._get_keywords(job_description_id, 'job_description')
            
            if not resume_keywords and not jd_keywords:
                return self._empty_score()
            
            # Calculate overall score using Jaccard similarity
            overall_score = self._calculate_jaccard_similarity(resume_keywords, jd_keywords)
            
            # Calculate category-specific scores
            technical_score = self._calculate_category_score(resume_keywords, jd_keywords, 'technical')
            soft_skills_score = self._calculate_category_score(resume_keywords, jd_keywords, 'soft_skill')
            experience_score = self._calculate_category_score(resume_keywords, jd_keywords, 'experience')
            
            # Find matched and missing keywords
            matched_keywords = self._find_matched_keywords(resume_keywords, jd_keywords)
            missing_keywords = self._find_missing_keywords(resume_keywords, jd_keywords)
            
            return {
                'overall_score': overall_score,
                'technical_score': technical_score,
                'soft_skills_score': soft_skills_score,
                'experience_score': experience_score,
                'matched_keywords': matched_keywords,
                'missing_keywords': missing_keywords
            }
            
        except Exception as e:
            print(f"Error calculating match score: {e}")
            return self._empty_score()
    
    def _get_keywords(self, entity_id: int, entity_type: str) -> List[Dict]:
        """Get keywords for resume or job description"""
        try:
            if entity_type == 'resume':
                keywords = Keyword.query.filter_by(resume_id=entity_id).all()
            else:
                keywords = Keyword.query.filter_by(job_description_id=entity_id).all()
            
            return [
                {
                    'keyword': k.keyword,
                    'category': k.category,
                    'frequency': k.frequency,
                    'confidence': k.confidence_score
                }
                for k in keywords
            ]
        except Exception as e:
            print(f"Error getting keywords: {e}")
            return []
    
    def _calculate_jaccard_similarity(self, resume_keywords: List[Dict], jd_keywords: List[Dict]) -> float:
        """Calculate Jaccard similarity between two keyword sets"""
        try:
            # Convert to sets of keywords
            resume_set = set(k['keyword'].lower() for k in resume_keywords)
            jd_set = set(k['keyword'].lower() for k in jd_keywords)
            
            if not resume_set and not jd_set:
                return 0.0
            
            # Calculate Jaccard similarity
            intersection = len(resume_set.intersection(jd_set))
            union = len(resume_set.union(jd_set))
            
            if union == 0:
                return 0.0
            
            return intersection / union
            
        except Exception as e:
            print(f"Error calculating Jaccard similarity: {e}")
            return 0.0
    
    def _calculate_category_score(self, resume_keywords: List[Dict], jd_keywords: List[Dict], category_prefix: str) -> float:
        """Calculate score for specific category"""
        try:
            # Filter keywords by category
            resume_cat = [k for k in resume_keywords if k['category'].startswith(category_prefix)]
            jd_cat = [k for k in jd_keywords if k['category'].startswith(category_prefix)]
            
            if not resume_cat and not jd_cat:
                return 0.0
            
            # Calculate weighted similarity
            resume_set = set(k['keyword'].lower() for k in resume_cat)
            jd_set = set(k['keyword'].lower() for k in jd_cat)
            
            if not jd_set:  # No requirements in this category
                return 1.0 if resume_set else 0.0
            
            intersection = len(resume_set.intersection(jd_set))
            return intersection / len(jd_set) if jd_set else 0.0
            
        except Exception as e:
            print(f"Error calculating category score: {e}")
            return 0.0
    
    def _find_matched_keywords(self, resume_keywords: List[Dict], jd_keywords: List[Dict]) -> List[str]:
        """Find keywords that match between resume and job description"""
        try:
            resume_set = set(k['keyword'].lower() for k in resume_keywords)
            jd_set = set(k['keyword'].lower() for k in jd_keywords)
            
            matched = list(resume_set.intersection(jd_set))
            return sorted(matched)
            
        except Exception as e:
            print(f"Error finding matched keywords: {e}")
            return []
    
    def _find_missing_keywords(self, resume_keywords: List[Dict], jd_keywords: List[Dict]) -> List[str]:
        """Find keywords that are in job description but missing from resume"""
        try:
            resume_set = set(k['keyword'].lower() for k in resume_keywords)
            jd_set = set(k['keyword'].lower() for k in jd_keywords)
            
            missing = list(jd_set - resume_set)
            return sorted(missing)
            
        except Exception as e:
            print(f"Error finding missing keywords: {e}")
            return []
    
    def _empty_score(self) -> Dict:
        """Return empty score structure"""
        return {
            'overall_score': 0.0,
            'technical_score': 0.0,
            'soft_skills_score': 0.0,
            'experience_score': 0.0,
            'matched_keywords': [],
            'missing_keywords': []
        }
    
    def get_improvement_suggestions(self, missing_keywords: List[str]) -> List[str]:
        """Generate improvement suggestions based on missing keywords"""
        suggestions = []
        
        if not missing_keywords:
            return ["Great! Your resume matches well with the job requirements."]
        
        # Group missing keywords by category
        technical_missing = [k for k in missing_keywords if any(
            tech in k.lower() for tech_list in [
                ['python', 'java', 'javascript', 'react', 'angular', 'node'],
                ['sql', 'database', 'mysql', 'postgresql'],
                ['aws', 'azure', 'cloud', 'docker', 'kubernetes']
            ] for tech in tech_list
        )]
        
        if technical_missing:
            suggestions.append(f"Consider adding these technical skills: {', '.join(technical_missing[:5])}")
        
        soft_skills_missing = [k for k in missing_keywords if k.lower() in [
            'leadership', 'communication', 'teamwork', 'problem-solving'
        ]]
        
        if soft_skills_missing:
            suggestions.append(f"Highlight these soft skills: {', '.join(soft_skills_missing[:3])}")
        
        if len(missing_keywords) > 8:
            suggestions.append("Consider tailoring your resume more closely to this specific job description.")
        
        return suggestions[:5]  # Return top 5 suggestions
