from flask import Flask, request, jsonify, send_from_directory
from flask_sqlalchemy import SQLAlchemy
from flask_jwt_extended import JWTManager, create_access_token, jwt_required, get_jwt_identity
from flask_cors import CORS
from werkzeug.utils import secure_filename
from werkzeug.security import generate_password_hash, check_password_hash
import os
import json
from datetime import datetime, timedelta

from config import config
from models import (
    db, User, Resume, JobDescription, Keyword, MatchingScore,
    Suggestion, Analytics, init_db, get_user_stats
)

def create_app(config_name='development'):
    app = Flask(__name__)

    # Load configuration
    app.config.from_object(config[config_name])
    config[config_name].init_app(app)

    # Initialize extensions
    db.init_app(app)
    jwt = JWTManager(app)
    CORS(app, origins=app.config['CORS_ORIGINS'])

    # Import services
    from services.file_processor import FileProcessor
    from services.keyword_extractor import KeywordExtractor
    from services.matching_algorithm import MatchingAlgorithm
    from services.suggestion_engine import SuggestionEngine

    file_processor = FileProcessor()
    keyword_extractor = KeywordExtractor()
    matching_algorithm = MatchingAlgorithm()
    suggestion_engine = SuggestionEngine()

    # Helper function to log analytics
    def log_analytics(user_id, action_type, entity_id=None, entity_type=None, metadata=None):
        try:
            analytics = Analytics(
                user_id=user_id,
                action_type=action_type,
                entity_id=entity_id,
                entity_type=entity_type,
                metadata=metadata,
                ip_address=request.remote_addr,
                user_agent=request.headers.get('User-Agent', '')[:500]
            )
            db.session.add(analytics)
            db.session.commit()
        except Exception as e:
            print(f"Analytics logging error: {e}")

    # US-01: User Registration
    @app.route('/api/register', methods=['POST'])
    def register():
        try:
            data = request.get_json()

            # Validation
            if not data or not data.get('email') or not data.get('password'):
                return jsonify({'error': 'Email and password are required'}), 400

            if not data.get('first_name') or not data.get('last_name'):
                return jsonify({'error': 'First name and last name are required'}), 400

            # Check if user already exists
            if User.query.filter_by(email=data['email']).first():
                return jsonify({'error': 'Email already registered'}), 409

            # Create new user
            user = User(
                first_name=data['first_name'],
                last_name=data['last_name'],
                email=data['email']
            )
            user.set_password(data['password'])

            db.session.add(user)
            db.session.commit()

            return jsonify({
                'message': 'User registered successfully',
                'user': user.to_dict()
            }), 201

        except Exception as e:
            db.session.rollback()
            return jsonify({'error': str(e)}), 500

    # US-02: Login & JWT
    @app.route('/api/login', methods=['POST'])
    def login():
        try:
            data = request.get_json()

            if not data or not data.get('email') or not data.get('password'):
                return jsonify({'error': 'Email and password are required'}), 400

            user = User.query.filter_by(email=data['email']).first()

            if not user or not user.check_password(data['password']):
                return jsonify({'error': 'Invalid email or password'}), 401

            # Create access token
            access_token = create_access_token(
                identity=user.id,
                additional_claims={'is_premium': user.is_premium}
            )

            # Log analytics
            log_analytics(user.id, 'login')

            return jsonify({
                'message': 'Login successful',
                'access_token': access_token,
                'user': user.to_dict()
            }), 200

        except Exception as e:
            return jsonify({'error': str(e)}), 500

    # US-03: Resume Upload
    @app.route('/api/upload_resume', methods=['POST'])
    @jwt_required()
    def upload_resume():
        try:
            user_id = get_jwt_identity()

            if 'file' not in request.files:
                return jsonify({'error': 'No file provided'}), 400

            file = request.files['file']
            title = request.form.get('title', file.filename)

            if file.filename == '':
                return jsonify({'error': 'No file selected'}), 400

            if not file_processor.allowed_file(file.filename):
                return jsonify({'error': 'File type not allowed'}), 400

            # Save file
            filename = secure_filename(file.filename)
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S_')
            filename = timestamp + filename
            file_path = os.path.join(app.config['UPLOAD_FOLDER'], 'resumes', filename)
            file.save(file_path)

            # Extract text
            extracted_text = file_processor.extract_text(file_path)

            # Save to database
            resume = Resume(
                user_id=user_id,
                title=title,
                filename=file.filename,
                file_path=file_path,
                file_size=os.path.getsize(file_path),
                file_type=file.filename.split('.')[-1].lower(),
                extracted_text=extracted_text
            )

            db.session.add(resume)
            db.session.commit()

            # Extract keywords
            keywords = keyword_extractor.extract_keywords(extracted_text, 'resume')
            for keyword_data in keywords:
                keyword = Keyword(
                    resume_id=resume.id,
                    keyword=keyword_data['keyword'],
                    category=keyword_data['category'],
                    frequency=keyword_data['frequency'],
                    confidence_score=keyword_data['confidence']
                )
                db.session.add(keyword)

            db.session.commit()

            # Log analytics
            log_analytics(user_id, 'upload_resume', resume.id, 'resume', {
                'filename': file.filename,
                'file_size': resume.file_size,
                'keywords_extracted': len(keywords)
            })

            return jsonify({
                'message': 'Resume uploaded successfully',
                'resume': resume.to_dict(),
                'keywords_extracted': len(keywords)
            }), 201

        except Exception as e:
            db.session.rollback()
            return jsonify({'error': str(e)}), 500

    # Get user's resumes
    @app.route('/api/resumes', methods=['GET'])
    @jwt_required()
    def get_resumes():
        try:
            user_id = get_jwt_identity()
            resumes = Resume.query.filter_by(user_id=user_id).order_by(Resume.created_at.desc()).all()
            return jsonify({
                'resumes': [resume.to_dict() for resume in resumes]
            }), 200
        except Exception as e:
            return jsonify({'error': str(e)}), 500

    # US-04: Job Description Upload
    @app.route('/api/upload_jd', methods=['POST'])
    @jwt_required()
    def upload_job_description():
        try:
            user_id = get_jwt_identity()
            data = request.get_json()

            if not data or not data.get('title') or not data.get('description'):
                return jsonify({'error': 'Title and description are required'}), 400

            # Create job description
            jd = JobDescription(
                user_id=user_id,
                title=data['title'],
                company=data.get('company', ''),
                description=data['description'],
                location=data.get('location', ''),
                salary_range=data.get('salary_range', '')
            )

            db.session.add(jd)
            db.session.commit()

            # Extract keywords
            keywords = keyword_extractor.extract_keywords(data['description'], 'job_description')
            for keyword_data in keywords:
                keyword = Keyword(
                    job_description_id=jd.id,
                    keyword=keyword_data['keyword'],
                    category=keyword_data['category'],
                    frequency=keyword_data['frequency'],
                    confidence_score=keyword_data['confidence']
                )
                db.session.add(keyword)

            db.session.commit()

            # Log analytics
            log_analytics(user_id, 'upload_jd', jd.id, 'job_description', {
                'title': data['title'],
                'company': data.get('company', ''),
                'keywords_extracted': len(keywords)
            })

            return jsonify({
                'message': 'Job description created successfully',
                'job_description': jd.to_dict(),
                'keywords_extracted': len(keywords)
            }), 201

        except Exception as e:
            db.session.rollback()
            return jsonify({'error': str(e)}), 500

    # Get user's job descriptions
    @app.route('/api/job_descriptions', methods=['GET'])
    @jwt_required()
    def get_job_descriptions():
        try:
            user_id = get_jwt_identity()
            jds = JobDescription.query.filter_by(user_id=user_id).order_by(JobDescription.created_at.desc()).all()
            return jsonify({
                'job_descriptions': [jd.to_dict() for jd in jds]
            }), 200
        except Exception as e:
            return jsonify({'error': str(e)}), 500

    # US-06: Matching Score
    @app.route('/api/match_score', methods=['POST'])
    @jwt_required()
    def calculate_match_score():
        try:
            user_id = get_jwt_identity()
            data = request.get_json()

            if not data or not data.get('resume_id') or not data.get('job_description_id'):
                return jsonify({'error': 'Resume ID and Job Description ID are required'}), 400

            resume = Resume.query.filter_by(id=data['resume_id'], user_id=user_id).first()
            jd = JobDescription.query.filter_by(id=data['job_description_id'], user_id=user_id).first()

            if not resume or not jd:
                return jsonify({'error': 'Resume or Job Description not found'}), 404

            # Calculate matching score
            score_data = matching_algorithm.calculate_match(resume.id, jd.id)

            # Save matching score
            matching_score = MatchingScore(
                user_id=user_id,
                resume_id=resume.id,
                job_description_id=jd.id,
                overall_score=score_data['overall_score'],
                technical_score=score_data['technical_score'],
                soft_skills_score=score_data['soft_skills_score'],
                experience_score=score_data['experience_score'],
                matched_keywords=score_data['matched_keywords'],
                missing_keywords=score_data['missing_keywords']
            )

            db.session.add(matching_score)
            db.session.commit()

            # Log analytics
            log_analytics(user_id, 'match_analysis', matching_score.id, 'matching_score', {
                'resume_title': resume.title,
                'jd_title': jd.title,
                'overall_score': score_data['overall_score']
            })

            return jsonify({
                'message': 'Match score calculated successfully',
                'matching_score': matching_score.to_dict(),
                'resume_title': resume.title,
                'job_title': jd.title
            }), 200

        except Exception as e:
            db.session.rollback()
            return jsonify({'error': str(e)}), 500

    # US-07: Basic Suggestions
    @app.route('/api/suggestions', methods=['POST'])
    @jwt_required()
    def get_suggestions():
        try:
            user_id = get_jwt_identity()
            data = request.get_json()

            if not data or not data.get('resume_id') or not data.get('job_description_id'):
                return jsonify({'error': 'Resume ID and Job Description ID are required'}), 400

            # Get basic suggestions
            suggestions = suggestion_engine.generate_basic_suggestions(
                data['resume_id'],
                data['job_description_id']
            )

            # Save suggestions to database
            saved_suggestions = []
            for suggestion_text in suggestions:
                suggestion = Suggestion(
                    user_id=user_id,
                    resume_id=data['resume_id'],
                    job_description_id=data['job_description_id'],
                    suggestion_type='basic',
                    suggestion_text=suggestion_text,
                    category='keywords'
                )
                db.session.add(suggestion)
                saved_suggestions.append(suggestion)

            db.session.commit()

            # Log analytics
            log_analytics(user_id, 'view_suggestions', None, 'suggestion', {
                'suggestion_count': len(suggestions),
                'suggestion_type': 'basic'
            })

            return jsonify({
                'suggestions': [s.to_dict() for s in saved_suggestions]
            }), 200

        except Exception as e:
            db.session.rollback()
            return jsonify({'error': str(e)}), 500

    # US-07.1: Premium OpenAI Suggestions
    @app.route('/api/premium_suggestions', methods=['POST'])
    @jwt_required()
    def get_premium_suggestions():
        try:
            user_id = get_jwt_identity()
            user = User.query.get(user_id)

            if not user.is_premium:
                return jsonify({'error': 'Premium subscription required'}), 403

            data = request.get_json()

            if not data or not data.get('resume_id') or not data.get('job_description_id'):
                return jsonify({'error': 'Resume ID and Job Description ID are required'}), 400

            # Get premium suggestions using OpenAI
            suggestions = suggestion_engine.generate_premium_suggestions(
                data['resume_id'],
                data['job_description_id']
            )

            # Save suggestions to database
            saved_suggestions = []
            for suggestion_data in suggestions:
                suggestion = Suggestion(
                    user_id=user_id,
                    resume_id=data['resume_id'],
                    job_description_id=data['job_description_id'],
                    suggestion_type='premium',
                    suggestion_text=suggestion_data['text'],
                    category=suggestion_data['category'],
                    priority=suggestion_data['priority']
                )
                db.session.add(suggestion)
                saved_suggestions.append(suggestion)

            db.session.commit()

            # Log analytics
            log_analytics(user_id, 'view_premium_suggestions', None, 'suggestion', {
                'suggestion_count': len(suggestions),
                'suggestion_type': 'premium'
            })

            return jsonify({
                'suggestions': [s.to_dict() for s in saved_suggestions]
            }), 200

        except Exception as e:
            db.session.rollback()
            return jsonify({'error': str(e)}), 500

    # US-08: Dashboard & Analytics
    @app.route('/api/dashboard', methods=['GET'])
    @jwt_required()
    def get_dashboard():
        try:
            user_id = get_jwt_identity()

            # Get user statistics
            stats = get_user_stats(user_id)

            # Get recent activity
            recent_resumes = Resume.query.filter_by(user_id=user_id).order_by(Resume.created_at.desc()).limit(5).all()
            recent_jds = JobDescription.query.filter_by(user_id=user_id).order_by(JobDescription.created_at.desc()).limit(5).all()
            recent_matches = MatchingScore.query.filter_by(user_id=user_id).order_by(MatchingScore.created_at.desc()).limit(5).all()

            return jsonify({
                'stats': stats,
                'recent_activity': {
                    'resumes': [r.to_dict() for r in recent_resumes],
                    'job_descriptions': [jd.to_dict() for jd in recent_jds],
                    'matches': [m.to_dict() for m in recent_matches]
                }
            }), 200

        except Exception as e:
            return jsonify({'error': str(e)}), 500

    # Get scan history
    @app.route('/api/history', methods=['GET'])
    @jwt_required()
    def get_history():
        try:
            user_id = get_jwt_identity()

            # Get all matching scores with related data
            matches = db.session.query(MatchingScore, Resume, JobDescription).join(
                Resume, MatchingScore.resume_id == Resume.id
            ).join(
                JobDescription, MatchingScore.job_description_id == JobDescription.id
            ).filter(MatchingScore.user_id == user_id).order_by(
                MatchingScore.created_at.desc()
            ).all()

            history = []
            for match, resume, jd in matches:
                history.append({
                    'id': match.id,
                    'resume_title': resume.title,
                    'job_title': jd.title,
                    'company': jd.company,
                    'overall_score': round(match.overall_score * 100, 2),
                    'created_at': match.created_at.isoformat(),
                    'matched_keywords': match.matched_keywords or [],
                    'missing_keywords': match.missing_keywords or []
                })

            return jsonify({'history': history}), 200

        except Exception as e:
            return jsonify({'error': str(e)}), 500

    # US-10: Account Management
    @app.route('/api/account', methods=['GET'])
    @jwt_required()
    def get_account():
        try:
            user_id = get_jwt_identity()
            user = User.query.get(user_id)

            if not user:
                return jsonify({'error': 'User not found'}), 404

            return jsonify({'user': user.to_dict()}), 200

        except Exception as e:
            return jsonify({'error': str(e)}), 500

    @app.route('/api/account', methods=['PUT'])
    @jwt_required()
    def update_account():
        try:
            user_id = get_jwt_identity()
            user = User.query.get(user_id)

            if not user:
                return jsonify({'error': 'User not found'}), 404

            data = request.get_json()

            # Update user information
            if 'first_name' in data:
                user.first_name = data['first_name']
            if 'last_name' in data:
                user.last_name = data['last_name']
            if 'email' in data:
                # Check if email is already taken
                existing_user = User.query.filter_by(email=data['email']).first()
                if existing_user and existing_user.id != user_id:
                    return jsonify({'error': 'Email already in use'}), 409
                user.email = data['email']

            user.updated_at = datetime.utcnow()
            db.session.commit()

            # Log analytics
            log_analytics(user_id, 'update_account', user_id, 'user')

            return jsonify({
                'message': 'Account updated successfully',
                'user': user.to_dict()
            }), 200

        except Exception as e:
            db.session.rollback()
            return jsonify({'error': str(e)}), 500

    @app.route('/api/change_password', methods=['POST'])
    @jwt_required()
    def change_password():
        try:
            user_id = get_jwt_identity()
            user = User.query.get(user_id)

            if not user:
                return jsonify({'error': 'User not found'}), 404

            data = request.get_json()

            if not data or not data.get('current_password') or not data.get('new_password'):
                return jsonify({'error': 'Current password and new password are required'}), 400

            # Verify current password
            if not user.check_password(data['current_password']):
                return jsonify({'error': 'Current password is incorrect'}), 401

            # Update password
            user.set_password(data['new_password'])
            user.updated_at = datetime.utcnow()
            db.session.commit()

            # Log analytics
            log_analytics(user_id, 'change_password', user_id, 'user')

            return jsonify({'message': 'Password changed successfully'}), 200

        except Exception as e:
            db.session.rollback()
            return jsonify({'error': str(e)}), 500

    # Token verification endpoint
    @app.route('/api/verify_token', methods=['GET'])
    @jwt_required()
    def verify_token():
        try:
            user_id = get_jwt_identity()
            user = User.query.get(user_id)

            if not user:
                return jsonify({'error': 'User not found'}), 404

            return jsonify({
                'valid': True,
                'user': user.to_dict()
            }), 200

        except Exception as e:
            return jsonify({'error': str(e)}), 500

    # Health check endpoint
    @app.route('/api/health', methods=['GET'])
    def health_check():
        return jsonify({
            'status': 'healthy',
            'timestamp': datetime.utcnow().isoformat(),
            'version': '1.0.0'
        }), 200

    # Initialize database
    with app.app_context():
        init_db(app)

    return app

# Create the application
app = create_app()

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
