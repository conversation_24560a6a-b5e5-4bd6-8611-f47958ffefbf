# US-1: User Signup - <PERSON>. <PERSON>sume AI

This guide explains how to implement user signup functionality using FastAPI for the backend and a simple HTML form for the frontend.

---

## 1. Backend Implementation

### a. Signup Endpoint
- **File:** `backend/routers/auth.py`
- **Purpose:** Allows new users to create an account by providing an email and password.
- **Key Steps:**
  1. Validates that the email is not already registered.
  2. Hashes the user's password for secure storage.
  3. Creates a new user record in the database.
  4. Returns a success message upon completion.
- **Code:**
```python
from fastapi import APIRouter, HTTPException, status, Depends
from sqlalchemy.orm import Session
from pydantic import BaseModel, EmailStr
from passlib.context import CryptContext
from ..database import SessionLocal
from ..models.user_model import User

router = APIRouter()
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

class UserCreate(BaseModel):
    email: EmailStr
    password: str

@router.post("/signup", status_code=status.HTTP_201_CREATED)
def signup(user_create: UserCreate, db: Session = Depends(get_db)):
    existing_user = db.query(User).filter(User.email == user_create.email).first()
    if existing_user:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Email already registered")
    
    hashed_password = pwd_context.hash(user_create.password)
    new_user = User(email=user_create.email, hashed_password=hashed_password)
    
    db.add(new_user)
    db.commit()
    db.refresh(new_user)
    
    return {"success": True, "message": "User created successfully"}
```

### b. Registering the Router
- **File:** `backend/main.py`
- **Purpose:** Integrates the signup endpoint into the main FastAPI application.
- **Code Addition:**
```python
from routers import auth

app.include_router(auth.router, prefix="/api", tags=["Authentication"])
```

---

## 2. Frontend Implementation

### a. Signup Page
- **File:** `frontend/signup.html`
- **Purpose:** Provides a form for users to sign up for a new account.
- **Features:**
  - Email and password input fields.
  - A "Sign Up" button to submit the form.
  - Displays a success or error message after submission.
- **Code:**
```html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Sign Up - Dr. Resume</title>
</head>
<body>
    <h2>Create an Account</h2>
    <form id="signupForm">
        <label>Email:</label><br>
        <input type="email" id="email" required><br>
        <label>Password:</label><br>
        <input type="password" id="password" required><br><br>
        <button type="submit">Sign Up</button>
    </form>
    <div id="message"></div>
    <script>
        document.getElementById('signupForm').onsubmit = async function(e) {
            e.preventDefault();
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            const res = await fetch('/api/signup', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ email, password })
            });
            
            const data = await res.json();
            
            if (res.status === 201) {
                document.getElementById('message').innerText = 'Signup successful! You can now log in.';
                window.location.href = '/login.html'; // Redirect to login page
            } else {
                document.getElementById('message').innerText = data.detail || 'An error occurred.';
            }
        };
    </script>
</body>
</html>
```

---

## 3. Integration & Flow
- The user fills out the signup form in `signup.html` and clicks "Sign Up".
- The frontend sends a POST request to the `/api/signup` endpoint with the user's email and password.
- The backend validates the data, creates the new user, and stores the hashed password.
- The frontend receives a confirmation and redirects the user to the login page.

---

## 4. How to Build This Feature (Step-by-Step)
1. **Define the User model** in `models/user_model.py` (if not already done).
2. **Create the signup endpoint** in `routers/auth.py`.
3. **Register the auth router** in `main.py`.
4. **Build the signup page** in `frontend/signup.html`.
5. **Test the entire flow**: Create a new user and verify they are added to the database.

---

## 5. Security Notes
- **Password Hashing:** Always hash passwords using a strong algorithm like bcrypt. Never store plaintext passwords.
- **Input Validation:** Use Pydantic models in FastAPI to validate incoming data, like ensuring the email is in a valid format.
- **Error Messages:** Avoid revealing whether an email address is already registered in production to prevent user enumeration attacks. A generic "Invalid credentials" or "Error" message can be safer.

---

This guide provides a solid foundation for adding a secure user signup feature to your application.