# US-2: User Login - <PERSON>. <PERSON>sume AI

This guide explains how to implement user login functionality, which authenticates users and provides them with a JSON Web Token (JWT) for accessing protected routes.

---

## 1. Backend Implementation

### a. Login Endpoint
- **File:** `backend/routers/login.py`
- **Purpose:** Authenticates users based on their email and password.
- **Key Steps:**
  1. Finds the user by email in the database.
  2. Verifies the provided password against the stored hash.
  3. If authentication is successful, generates a JWT.
  4. Returns the JWT to the client.
- **Code:**
```python
from fastapi import APIRouter, HTTPException, status, Depends
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.orm import Session
from passlib.context import CryptContext
from ..database import SessionLocal
from ..models.user_model import User
from .jwt_utils import create_access_token

router = APIRouter()
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

@router.post("/login")
def login(form_data: OAuth2PasswordRequestForm = Depends(), db: Session = Depends(get_db)):
    user = db.query(User).filter(User.email == form_data.username).first()
    
    if not user or not pwd_context.verify(form_data.password, user.hashed_password):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect email or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    access_token = create_access_token(data={"user_id": user.id, "email": user.email})
    
    return {"access_token": access_token, "token_type": "bearer"}
```

### b. JWT Utility
- **File:** `backend/routers/jwt_utils.py`
- **Purpose:** Contains helper functions for creating and verifying JWTs.
- **Code:**
```python
from datetime import datetime, timedelta
from jose import JWTError, jwt

SECRET_KEY = "YOUR_SECRET_KEY"  # Use a strong, unique secret key
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

def create_access_token(data: dict):
    to_encode = data.copy()
    expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

def verify_token(token: str):
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        return payload
    except JWTError:
        return None
```

### c. Registering the Router
- **File:** `backend/main.py`
- **Purpose:** Integrates the login endpoint into the FastAPI application.
- **Code Addition:**
```python
from routers import login # ... and other routers

app.include_router(login.router, prefix="/api", tags=["Authentication"])
```

---

## 2. Frontend Implementation

### a. Login Page
- **File:** `frontend/login.html`
- **Purpose:** Provides a form for users to log in.
- **Features:**
  - Email and password input fields.
  - A "Login" button.
  - Stores the JWT in `localStorage` upon successful login.
  - Redirects to the main application page (e.g., dashboard).
- **Code:**
```html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Login - Dr. Resume</title>
</head>
<body>
    <h2>Login</h2>
    <form id="loginForm">
        <label>Email:</label><br>
        <input type="email" id="email" required><br>
        <label>Password:</label><br>
        <input type="password" id="password" required><br><br>
        <button type="submit">Login</button>
    </form>
    <div id="message"></div>
    <script>
        document.getElementById('loginForm').onsubmit = async function(e) {
            e.preventDefault();
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            const formData = new URLSearchParams();
            formData.append('username', email);
            formData.append('password', password);

            const res = await fetch('/api/login', {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                body: formData
            });
            
            const data = await res.json();
            
            if (res.ok) {
                localStorage.setItem('token', data.access_token);
                window.location.href = '/dashboard.html'; // Redirect to a protected page
            } else {
                document.getElementById('message').innerText = data.detail || 'Login failed.';
            }
        };
    </script>
</body>
</html>
```

---

## 3. Integration & Flow
- The user enters their credentials on the `login.html` page.
- The frontend sends a POST request to `/api/login` with the credentials.
- The backend verifies the credentials, generates a JWT, and returns it.
- The frontend stores the token in `localStorage` and redirects the user to a protected part of the application.

---

## 4. How to Build This Feature (Step-by-Step)
1. **Install necessary libraries**: `pip install python-jose[cryptography] passlib[bcrypt]`.
2. **Create the JWT utility functions** in `routers/jwt_utils.py`.
3. **Implement the login endpoint** in `routers/login.py`.
4. **Register the login router** in `main.py`.
5. **Build the login form** in `frontend/login.html`.
6. **Test the flow**: Log in with a valid user and ensure the token is stored correctly.

---

## 5. Security Notes
- **Use HTTPS:** Always use HTTPS in production to protect tokens and other data in transit.
- **Secret Key Management:** Store your `SECRET_KEY` securely. Do not hardcode it directly in the source code; use environment variables or a secrets management system.
- **Token Expiration:** Set a reasonable expiration time for JWTs to limit the window of opportunity for token theft.
- **Token Storage:** `localStorage` is convenient but vulnerable to XSS attacks. For higher security applications, consider using `HttpOnly` cookies.

---

This setup provides a standard and secure way to handle user authentication.