import os
import openai
from typing import List, Dict
from models import db, Resume, JobDescription, Keyword

class SuggestionEngine:
    """AI-powered suggestion engine for resume optimization"""
    
    def __init__(self):
        self.openai_api_key = os.getenv('OPENAI_API_KEY')
        if self.openai_api_key:
            openai.api_key = self.openai_api_key
    
    def generate_basic_suggestions(self, resume_id: int, job_description_id: int) -> List[str]:
        """Generate basic suggestions based on keyword analysis"""
        try:
            # Get resume and job description
            resume = Resume.query.get(resume_id)
            jd = JobDescription.query.get(job_description_id)
            
            if not resume or not jd:
                return ["Unable to generate suggestions: Resume or Job Description not found."]
            
            # Get keywords
            resume_keywords = self._get_keywords(resume_id, 'resume')
            jd_keywords = self._get_keywords(job_description_id, 'job_description')
            
            # Find missing keywords
            missing_keywords = self._find_missing_keywords(resume_keywords, jd_keywords)
            
            suggestions = []
            
            if not missing_keywords:
                suggestions.append("Excellent! Your resume aligns well with the job requirements.")
                return suggestions
            
            # Generate suggestions based on missing keywords
            technical_missing = [k for k in missing_keywords if self._is_technical_keyword(k)]
            soft_skills_missing = [k for k in missing_keywords if self._is_soft_skill(k)]
            
            if technical_missing:
                suggestions.append(f"Consider adding these technical skills to your resume: {', '.join(technical_missing[:5])}")
            
            if soft_skills_missing:
                suggestions.append(f"Highlight these soft skills in your experience descriptions: {', '.join(soft_skills_missing[:3])}")
            
            # General suggestions
            if len(missing_keywords) > 10:
                suggestions.append("Your resume could be more tailored to this specific role. Consider customizing it further.")
            
            if len(missing_keywords) > 5:
                suggestions.append("Review the job description carefully and incorporate more relevant keywords naturally into your resume.")
            
            # Format suggestions
            suggestions.append("Use action verbs and quantify your achievements where possible.")
            suggestions.append("Ensure your resume format is ATS-friendly with clear section headers.")
            
            return suggestions[:6]  # Return top 6 suggestions
            
        except Exception as e:
            print(f"Error generating basic suggestions: {e}")
            return ["Unable to generate suggestions at this time."]
    
    def generate_premium_suggestions(self, resume_id: int, job_description_id: int) -> List[Dict]:
        """Generate premium AI-powered suggestions using OpenAI"""
        try:
            if not self.openai_api_key:
                return [{
                    'text': 'Premium suggestions require OpenAI API configuration.',
                    'category': 'configuration',
                    'priority': 'high'
                }]
            
            # Get resume and job description
            resume = Resume.query.get(resume_id)
            jd = JobDescription.query.get(job_description_id)
            
            if not resume or not jd:
                return [{
                    'text': 'Unable to generate suggestions: Resume or Job Description not found.',
                    'category': 'error',
                    'priority': 'high'
                }]
            
            # Prepare prompt for OpenAI
            prompt = self._create_openai_prompt(resume.extracted_text, jd.description, jd.title)
            
            # Call OpenAI API
            response = openai.ChatCompletion.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": "You are an expert resume consultant and career advisor."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=1000,
                temperature=0.7
            )
            
            # Parse response
            suggestions_text = response.choices[0].message.content
            suggestions = self._parse_openai_response(suggestions_text)
            
            return suggestions
            
        except Exception as e:
            print(f"Error generating premium suggestions: {e}")
            return [{
                'text': 'Unable to generate premium suggestions at this time. Please try again later.',
                'category': 'error',
                'priority': 'medium'
            }]
    
    def _get_keywords(self, entity_id: int, entity_type: str) -> List[str]:
        """Get keywords for resume or job description"""
        try:
            if entity_type == 'resume':
                keywords = Keyword.query.filter_by(resume_id=entity_id).all()
            else:
                keywords = Keyword.query.filter_by(job_description_id=entity_id).all()
            
            return [k.keyword.lower() for k in keywords]
        except Exception as e:
            print(f"Error getting keywords: {e}")
            return []
    
    def _find_missing_keywords(self, resume_keywords: List[str], jd_keywords: List[str]) -> List[str]:
        """Find keywords missing from resume"""
        resume_set = set(resume_keywords)
        jd_set = set(jd_keywords)
        return list(jd_set - resume_set)
    
    def _is_technical_keyword(self, keyword: str) -> bool:
        """Check if keyword is technical"""
        technical_terms = [
            'python', 'java', 'javascript', 'react', 'angular', 'node', 'sql', 'aws', 'azure',
            'docker', 'kubernetes', 'git', 'html', 'css', 'mongodb', 'postgresql', 'mysql'
        ]
        return keyword.lower() in technical_terms
    
    def _is_soft_skill(self, keyword: str) -> bool:
        """Check if keyword is a soft skill"""
        soft_skills = [
            'leadership', 'communication', 'teamwork', 'problem-solving', 'analytical',
            'creative', 'adaptable', 'organized', 'detail-oriented', 'time-management'
        ]
        return keyword.lower() in soft_skills
    
    def _create_openai_prompt(self, resume_text: str, job_description: str, job_title: str) -> str:
        """Create prompt for OpenAI API"""
        prompt = f"""
        As an expert resume consultant, analyze the following resume against the job description and provide specific, actionable suggestions for improvement.

        Job Title: {job_title}

        Job Description:
        {job_description[:1000]}...

        Resume Content:
        {resume_text[:1500]}...

        Please provide 5-7 specific suggestions in the following format:
        1. [CATEGORY: Skills/Experience/Format/Keywords] - [PRIORITY: High/Medium/Low] - [Specific actionable suggestion]

        Focus on:
        - Missing technical skills or certifications
        - Experience gaps or areas to emphasize
        - Keyword optimization for ATS systems
        - Quantifiable achievements to add
        - Format and structure improvements
        """
        return prompt
    
    def _parse_openai_response(self, response_text: str) -> List[Dict]:
        """Parse OpenAI response into structured suggestions"""
        suggestions = []
        lines = response_text.strip().split('\n')
        
        for line in lines:
            line = line.strip()
            if not line or not line[0].isdigit():
                continue
            
            try:
                # Parse format: "1. [CATEGORY: Skills] - [PRIORITY: High] - Suggestion text"
                parts = line.split(' - ', 2)
                if len(parts) >= 3:
                    category_part = parts[0].split('[CATEGORY:')[1].split(']')[0].strip() if '[CATEGORY:' in parts[0] else 'general'
                    priority_part = parts[1].split('[PRIORITY:')[1].split(']')[0].strip().lower() if '[PRIORITY:' in parts[1] else 'medium'
                    suggestion_text = parts[2].strip()
                    
                    suggestions.append({
                        'text': suggestion_text,
                        'category': category_part.lower(),
                        'priority': priority_part
                    })
                else:
                    # Fallback parsing
                    suggestion_text = line.split('.', 1)[1].strip() if '.' in line else line
                    suggestions.append({
                        'text': suggestion_text,
                        'category': 'general',
                        'priority': 'medium'
                    })
            except Exception as e:
                print(f"Error parsing suggestion line: {e}")
                continue
        
        # If no suggestions parsed, create a generic one
        if not suggestions:
            suggestions.append({
                'text': 'Review your resume to better align with the job requirements.',
                'category': 'general',
                'priority': 'medium'
            })
        
        return suggestions[:7]  # Return max 7 suggestions
