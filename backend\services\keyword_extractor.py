import re
from collections import Counter
from typing import List, Dict

class KeywordExtractor:
    """Keyword extraction service using lightweight NLP approach"""
    
    def __init__(self):
        # Technical skills keywords
        self.technical_skills = {
            'programming': ['python', 'java', 'javascript', 'c++', 'c#', 'php', 'ruby', 'go', 'rust', 'swift', 'kotlin'],
            'web': ['html', 'css', 'react', 'angular', 'vue', 'node.js', 'express', 'django', 'flask', 'spring'],
            'database': ['sql', 'mysql', 'postgresql', 'mongodb', 'redis', 'elasticsearch', 'oracle', 'sqlite'],
            'cloud': ['aws', 'azure', 'gcp', 'docker', 'kubernetes', 'terraform', 'jenkins', 'gitlab'],
            'tools': ['git', 'jira', 'confluence', 'slack', 'figma', 'photoshop', 'excel', 'powerpoint'],
            'frameworks': ['tensorflow', 'pytorch', 'scikit-learn', 'pandas', 'numpy', 'matplotlib', 'seaborn']
        }
        
        # Soft skills keywords
        self.soft_skills = [
            'leadership', 'communication', 'teamwork', 'problem-solving', 'analytical',
            'creative', 'adaptable', 'organized', 'detail-oriented', 'time-management',
            'collaboration', 'presentation', 'negotiation', 'mentoring', 'coaching'
        ]
        
        # Experience level indicators
        self.experience_indicators = {
            'junior': ['junior', 'entry-level', 'graduate', 'intern', 'trainee', '0-2 years'],
            'mid': ['mid-level', 'intermediate', 'experienced', '3-5 years', '2-4 years'],
            'senior': ['senior', 'lead', 'principal', 'architect', '5+ years', '6+ years', 'expert'],
            'executive': ['director', 'manager', 'head', 'chief', 'vp', 'vice president', 'ceo', 'cto']
        }
        
        # Education keywords
        self.education_keywords = [
            'bachelor', 'master', 'phd', 'degree', 'university', 'college',
            'certification', 'certified', 'diploma', 'course', 'training'
        ]
    
    def extract_keywords(self, text: str, content_type: str = 'resume') -> List[Dict]:
        """Extract keywords from text and categorize them"""
        if not text:
            return []
        
        text = text.lower()
        keywords = []
        
        # Extract technical skills
        tech_keywords = self._extract_technical_skills(text)
        keywords.extend(tech_keywords)
        
        # Extract soft skills
        soft_keywords = self._extract_soft_skills(text)
        keywords.extend(soft_keywords)
        
        # Extract experience level
        exp_keywords = self._extract_experience_level(text)
        keywords.extend(exp_keywords)
        
        # Extract education
        edu_keywords = self._extract_education(text)
        keywords.extend(edu_keywords)
        
        # Extract general keywords (nouns and important terms)
        general_keywords = self._extract_general_keywords(text)
        keywords.extend(general_keywords)
        
        return keywords
    
    def _extract_technical_skills(self, text: str) -> List[Dict]:
        """Extract technical skills from text"""
        keywords = []
        
        for category, skills in self.technical_skills.items():
            for skill in skills:
                # Use word boundaries to match exact skills
                pattern = r'\b' + re.escape(skill.lower()) + r'\b'
                matches = re.findall(pattern, text)
                
                if matches:
                    keywords.append({
                        'keyword': skill,
                        'category': f'technical_{category}',
                        'frequency': len(matches),
                        'confidence': min(1.0, len(matches) * 0.3)
                    })
        
        return keywords
    
    def _extract_soft_skills(self, text: str) -> List[Dict]:
        """Extract soft skills from text"""
        keywords = []
        
        for skill in self.soft_skills:
            pattern = r'\b' + re.escape(skill.lower()) + r'\b'
            matches = re.findall(pattern, text)
            
            if matches:
                keywords.append({
                    'keyword': skill,
                    'category': 'soft_skill',
                    'frequency': len(matches),
                    'confidence': min(1.0, len(matches) * 0.4)
                })
        
        return keywords
    
    def _extract_experience_level(self, text: str) -> List[Dict]:
        """Extract experience level indicators"""
        keywords = []
        
        for level, indicators in self.experience_indicators.items():
            for indicator in indicators:
                pattern = r'\b' + re.escape(indicator.lower()) + r'\b'
                matches = re.findall(pattern, text)
                
                if matches:
                    keywords.append({
                        'keyword': indicator,
                        'category': f'experience_{level}',
                        'frequency': len(matches),
                        'confidence': min(1.0, len(matches) * 0.5)
                    })
        
        return keywords
    
    def _extract_education(self, text: str) -> List[Dict]:
        """Extract education-related keywords"""
        keywords = []
        
        for edu_term in self.education_keywords:
            pattern = r'\b' + re.escape(edu_term.lower()) + r'\b'
            matches = re.findall(pattern, text)
            
            if matches:
                keywords.append({
                    'keyword': edu_term,
                    'category': 'education',
                    'frequency': len(matches),
                    'confidence': min(1.0, len(matches) * 0.3)
                })
        
        return keywords
    
    def _extract_general_keywords(self, text: str) -> List[Dict]:
        """Extract general important keywords"""
        keywords = []
        
        # Remove common stop words and extract meaningful terms
        stop_words = {
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with',
            'by', 'from', 'up', 'about', 'into', 'through', 'during', 'before', 'after',
            'above', 'below', 'between', 'among', 'is', 'are', 'was', 'were', 'be', 'been',
            'being', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could',
            'should', 'may', 'might', 'must', 'can', 'this', 'that', 'these', 'those'
        }
        
        # Extract words that are 3+ characters and not stop words
        words = re.findall(r'\b[a-zA-Z]{3,}\b', text)
        word_freq = Counter(word.lower() for word in words if word.lower() not in stop_words)
        
        # Get top frequent words
        for word, freq in word_freq.most_common(20):
            if freq >= 2:  # Only include words that appear at least twice
                keywords.append({
                    'keyword': word,
                    'category': 'general',
                    'frequency': freq,
                    'confidence': min(1.0, freq * 0.1)
                })
        
        return keywords
    
    def get_keyword_summary(self, keywords: List[Dict]) -> Dict:
        """Get summary of extracted keywords by category"""
        summary = {}
        
        for keyword in keywords:
            category = keyword['category']
            if category not in summary:
                summary[category] = []
            summary[category].append(keyword['keyword'])
        
        return summary
