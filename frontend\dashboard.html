<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - Dr. Resume AI</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Navigation -->
    <nav class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <h1 class="text-xl font-bold text-blue-600">Dr. Resume AI</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <span id="userWelcome" class="text-gray-600"></span>
                    <button onclick="logout()" class="text-gray-600 hover:text-red-600">Logout</button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Welcome Section -->
        <div class="mb-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-2">Dashboard</h2>
            <p class="text-gray-600">Manage your resumes, job descriptions, and analysis results</p>
        </div>

        <!-- Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white p-6 rounded-lg shadow-sm border">
                <div class="flex items-center">
                    <div class="p-2 bg-blue-100 rounded-lg">
                        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Resumes</p>
                        <p id="resumeCount" class="text-2xl font-semibold text-gray-900">0</p>
                    </div>
                </div>
            </div>

            <div class="bg-white p-6 rounded-lg shadow-sm border">
                <div class="flex items-center">
                    <div class="p-2 bg-green-100 rounded-lg">
                        <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m8 0H8m8 0v2a2 2 0 01-2 2H10a2 2 0 01-2-2V8m8 0V6a2 2 0 00-2-2H10a2 2 0 00-2 2v2"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Job Descriptions</p>
                        <p id="jdCount" class="text-2xl font-semibold text-gray-900">0</p>
                    </div>
                </div>
            </div>

            <div class="bg-white p-6 rounded-lg shadow-sm border">
                <div class="flex items-center">
                    <div class="p-2 bg-purple-100 rounded-lg">
                        <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Analyses</p>
                        <p id="analysisCount" class="text-2xl font-semibold text-gray-900">0</p>
                    </div>
                </div>
            </div>

            <div class="bg-white p-6 rounded-lg shadow-sm border">
                <div class="flex items-center">
                    <div class="p-2 bg-yellow-100 rounded-lg">
                        <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Avg Match Score</p>
                        <p id="avgScore" class="text-2xl font-semibold text-gray-900">0%</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <button onclick="showUploadResume()" class="bg-blue-600 text-white p-6 rounded-lg hover:bg-blue-700 transition">
                <div class="text-center">
                    <svg class="w-8 h-8 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                    </svg>
                    <h3 class="text-lg font-semibold">Upload Resume</h3>
                    <p class="text-blue-100">Add a new resume for analysis</p>
                </div>
            </button>

            <button onclick="showAddJD()" class="bg-green-600 text-white p-6 rounded-lg hover:bg-green-700 transition">
                <div class="text-center">
                    <svg class="w-8 h-8 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    <h3 class="text-lg font-semibold">Add Job Description</h3>
                    <p class="text-green-100">Create a new job description</p>
                </div>
            </button>

            <button onclick="showAnalysis()" class="bg-purple-600 text-white p-6 rounded-lg hover:bg-purple-700 transition">
                <div class="text-center">
                    <svg class="w-8 h-8 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    <h3 class="text-lg font-semibold">Run Analysis</h3>
                    <p class="text-purple-100">Analyze resume vs job description</p>
                </div>
            </button>
        </div>

        <!-- Content Sections -->
        <div id="contentArea" class="bg-white rounded-lg shadow-sm border p-6">
            <div id="defaultContent">
                <h3 class="text-lg font-semibold mb-4">Recent Activity</h3>
                <div id="recentActivity">
                    <p class="text-gray-500">No recent activity. Start by uploading a resume or adding a job description.</p>
                </div>
            </div>

            <!-- Upload Resume Section -->
            <div id="uploadResumeSection" class="hidden">
                <h3 class="text-lg font-semibold mb-4">Upload Resume</h3>
                <form id="uploadResumeForm" class="space-y-4">
                    <div>
                        <label for="resumeTitle" class="block text-sm font-medium text-gray-700">Resume Title</label>
                        <input type="text" id="resumeTitle" required 
                               class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    <div>
                        <label for="resumeFile" class="block text-sm font-medium text-gray-700">Resume File</label>
                        <input type="file" id="resumeFile" accept=".pdf,.doc,.docx,.txt" required 
                               class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    <button type="submit" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                        Upload Resume
                    </button>
                </form>
                <div id="uploadResumeMessage" class="mt-4"></div>
            </div>

            <!-- Add Job Description Section -->
            <div id="addJDSection" class="hidden">
                <h3 class="text-lg font-semibold mb-4">Add Job Description</h3>
                <form id="addJDForm" class="space-y-4">
                    <div>
                        <label for="jobTitle" class="block text-sm font-medium text-gray-700">Job Title</label>
                        <input type="text" id="jobTitle" required 
                               class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    <div>
                        <label for="company" class="block text-sm font-medium text-gray-700">Company</label>
                        <input type="text" id="company" 
                               class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    <div>
                        <label for="jobDescription" class="block text-sm font-medium text-gray-700">Job Description</label>
                        <textarea id="jobDescription" rows="6" required 
                                  class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"></textarea>
                    </div>
                    <div>
                        <label for="location" class="block text-sm font-medium text-gray-700">Location</label>
                        <input type="text" id="location" 
                               class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    <button type="submit" class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700">
                        Add Job Description
                    </button>
                </form>
                <div id="addJDMessage" class="mt-4"></div>
            </div>

            <!-- Analysis Section -->
            <div id="analysisSection" class="hidden">
                <h3 class="text-lg font-semibold mb-4">Resume Analysis</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                        <label for="selectResume" class="block text-sm font-medium text-gray-700">Select Resume</label>
                        <select id="selectResume" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                            <option value="">Choose a resume...</option>
                        </select>
                    </div>
                    <div>
                        <label for="selectJD" class="block text-sm font-medium text-gray-700">Select Job Description</label>
                        <select id="selectJD" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                            <option value="">Choose a job description...</option>
                        </select>
                    </div>
                </div>
                <button onclick="runAnalysis()" class="bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700 mb-4">
                    Calculate Match Score
                </button>
                <div id="analysisResults" class="mt-4"></div>
            </div>
        </div>
    </div>

    <script src="dashboard.js"></script>
</body>
</html>
