from fastapi import APIRouter, HTTPException, status, Depends, Header
from sqlalchemy.orm import Session
from pydantic import BaseModel
from ..database import SessionLocal
from ..models.user_model import User
from ..models.jd_model import JobDescription
from .jwt_utils import verify_token

router = APIRouter()

def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

class JdCreate(BaseModel):
    title: str | None = None
    content: str

@router.post("/upload_jd")
def upload_jd(jd_create: JdCreate, Authorization: str = Header(None), db: Session = Depends(get_db)):
    if not Authorization or not Authorization.startswith("Bearer "):
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Missing or invalid token")
    token = Authorization.split(" ")[1]
    payload = verify_token(token)
    if not payload:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid token")
    user_id = payload.get("user_id")

    new_jd = JobDescription(user_id=user_id, title=jd_create.title, content=jd_create.content)
    db.add(new_jd)
    db.commit()
    db.refresh(new_jd)

    # Placeholder for local script parsing (US-05 will detail this)
    print(f"Simulating parsing of JD content for JD ID: {new_jd.id}...")

    return {"id": new_jd.id, "message": "Job Description uploaded successfully!"}