# US-7: JD Parsing - <PERSON>. <PERSON>sume AI

This guide explains how to implement Job Description (JD) parsing functionality, which extracts structured data from uploaded JDs. Similar to resume parsing, this version will use placeholder logic for the extracted data.

---

## 1. Backend Implementation

### a. Parsed Job Description Model
- **File:** `backend/models/parsed_jd_model.py`
- **Purpose:** Defines the database table structure to store the extracted, structured data from parsed job descriptions.
- **Key Steps:**
  1. Creates a `ParsedJobDescription` model with fields for `id`, `jd_id` (foreign key to `JobDescription`), `user_id` (foreign key to `User`), `text_content` (raw text extracted), and `parsed_data` (JSON string of structured data).
  2. Establishes relationships with `JobDescription` and `User` models.
- **Code:**
```python
from sqlalchemy import Column, Integer, String, ForeignKey, Text
from sqlalchemy.orm import relationship
from .database import Base

class ParsedJobDescription(Base):
    __tablename__ = "parsed_job_descriptions"

    id = Column(Integer, primary_key=True, index=True)
    jd_id = Column(Integer, ForeignKey("job_descriptions.id"), unique=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    text_content = Column(Text) # Extracted raw text
    parsed_data = Column(Text) # JSON string of parsed entities (skills, requirements, etc.)

    job_description = relationship("JobDescription")
    owner = relationship("User")
```

### b. JD Parsing Endpoint
- **File:** `backend/routers/parse_jd.py`
- **Purpose:** Receives a JD ID, simulates parsing its content, and stores the extracted data.
- **Key Steps:**
  1. Requires a valid JWT for authentication.
  2. Validates that the provided `jd_id` belongs to the authenticated user.
  3. Simulates extracting text content and structured data (e.g., title, company, requirements).
  4. Stores or updates the parsed data in the `parsed_job_descriptions` table.
- **Code:**
```python
from fastapi import APIRouter, HTTPException, status, Depends, Header
from sqlalchemy.orm import Session
from pydantic import BaseModel
from ..database import SessionLocal
from ..models.user_model import User
from ..models.jd_model import JobDescription
from ..models.parsed_jd_model import ParsedJobDescription
from .jwt_utils import verify_token
import json

router = APIRouter()

def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

class ParseJdRequest(BaseModel):
    jd_id: int

@router.post("/parse_jd")
def parse_jd(request: ParseJdRequest, Authorization: str = Header(None), db: Session = Depends(get_db)):
    if not Authorization or not Authorization.startswith("Bearer "):
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Missing or invalid token")
    token = Authorization.split(" ")[1]
    payload = verify_token(token)
    if not payload:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid token")
    user_id = payload.get("user_id")

    jd = db.query(JobDescription).filter(JobDescription.id == request.jd_id, JobDescription.user_id == user_id).first()
    if not jd:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Job Description not found or does not belong to user")

    # Placeholder for actual JD parsing logic
    # In a real application, this would involve calling an NLP service or library
    # For now, we'll simulate some parsed data
    parsed_text_content = f"Extracted text from {jd.filename}: This is a sample job description text content."
    parsed_data_json = {
        "title": "Software Engineer",
        "company": "Acme Corp",
        "location": "Remote",
        "requirements": ["Python", "FastAPI", "SQL", "AWS"],
        "responsibilities": ["Develop and maintain backend services", "Collaborate with frontend team"]
    }
    
    # Check if JD has already been parsed
    existing_parsed_jd = db.query(ParsedJobDescription).filter(ParsedJobDescription.jd_id == jd.id).first()
    if existing_parsed_jd:
        existing_parsed_jd.text_content = parsed_text_content
        existing_parsed_jd.parsed_data = json.dumps(parsed_data_json)
    else:
        new_parsed_jd = ParsedJobDescription(
            jd_id=jd.id,
            user_id=user_id,
            text_content=parsed_text_content,
            parsed_data=json.dumps(parsed_data_json)
        )
        db.add(new_parsed_jd)
    
    db.commit()
    
    return {"success": True, "message": "Job Description parsing initiated!", "parsed_data": parsed_data_json}
```

### c. Registering the Router
- **File:** `backend/main.py`
- **Purpose:** Integrates the JD parsing endpoint and the new `ParsedJobDescription` model into the FastAPI application.
- **Code Addition:**
```python
from .models import user_model, resume_model, jd_model, parsed_resume_model, parsed_jd_model
from .routers import auth, login, upload, jd_upload, match, parse, parse_jd

user_model.Base.metadata.create_all(bind=engine)
resume_model.Base.metadata.create_all(bind=engine)
jd_model.Base.metadata.create_all(bind=engine)
parsed_resume_model.Base.metadata.create_all(bind=engine)
parsed_jd_model.Base.metadata.create_all(bind=engine)

app.include_router(parse_jd.router, prefix="/api", tags=["JD Parsing"])
```

---

## 2. Frontend Implementation

### a. JD Parsing Page
- **File:** `frontend/parse_jd.html`
- **Purpose:** Allows users to select an uploaded job description and trigger the parsing process, then displays the (simulated) parsed data.
- **Features:**
  - Dropdown menu dynamically populated with the user's uploaded JDs.
  - A button to trigger the parsing process.
  - Displays the parsed data in a readable format.
- **Code:**
```html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Parse Job Description - Dr. Resume</title>
</head>
<body>
    <h2>Parse Job Description</h2>
    <form id="parseJdForm">
        <label for="jdSelect">Select Job Description to Parse:</label><br>
        <select id="jdSelect" required>
            <!-- JDs will be loaded here dynamically -->
        </select><br><br>

        <button type="submit">Parse JD</button>
    </form>
    <div id="message"></div>
    <div id="parsedResult" style="margin-top: 20px;">
        <h3>Parsed Data:</h3>
        <pre id="parsedDataDisplay"></pre>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', async () => {
            const token = localStorage.getItem('token');
            if (!token) {
                document.getElementById('message').innerText = 'You must be logged in to use this feature.';
                return;
            }

            // Function to fetch and populate JDs
            async function loadJDs() {
                const res = await fetch('/api/jds', {
                    headers: { 'Authorization': 'Bearer ' + token }
                });
                if (res.ok) {
                    const data = await res.json();
                    const jdSelect = document.getElementById('jdSelect');
                    jdSelect.innerHTML = '';
                    if (data.jds.length === 0) {
                        document.getElementById('message').innerText = 'No job descriptions uploaded yet. Please upload one first.';
                        return;
                    }
                    data.jds.forEach(jd => {
                        const option = document.createElement('option');
                        option.value = jd.id;
                        option.innerText = jd.filename;
                        jdSelect.appendChild(option);
                    });
                } else {
                    document.getElementById('message').innerText = 'Failed to load job descriptions.';
                }
            }

            await loadJDs();
        });

        document.getElementById('parseJdForm').onsubmit = async function(e) {
            e.preventDefault();
            const jdId = document.getElementById('jdSelect').value;
            const token = localStorage.getItem('token');

            if (!jdId) {
                document.getElementById('message').innerText = 'Please select a job description to parse.';
                return;
            }

            const res = await fetch('/api/parse_jd', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': 'Bearer ' + token
                },
                body: JSON.stringify({ jd_id: parseInt(jdId) })
            });
            
            const data = await res.json();
            
            if (res.ok) {
                document.getElementById('message').innerText = data.message;
                document.getElementById('parsedDataDisplay').innerText = JSON.stringify(data.parsed_data, null, 2);
            } else {
                document.getElementById('message').innerText = data.detail || 'An error occurred during parsing.';
                document.getElementById('parsedDataDisplay').innerText = '';
            }
        };
    </script>
</body>
</html>
```

---

## 3. Integration & Flow
- The user navigates to the `parse_jd.html` page.
- Upon loading, the frontend fetches the user's uploaded JDs to populate the dropdown.
- The user selects a JD and clicks "Parse JD".
- The frontend sends a POST request to `/api/parse_jd` with the selected JD ID and the user's JWT.
- The backend verifies ownership, performs the (simulated) parsing, and stores the result.
- The frontend displays the parsed data to the user.

---

## 4. How to Build This Feature (Step-by-Step)
1.  **Define the `ParsedJobDescription` model** in `backend/models/parsed_jd_model.py`.
2.  **Update `backend/main.py`** to include `parsed_jd_model` metadata creation and register the `parse_jd` router.
3.  **Create the parsing endpoint** in `backend/routers/parse_jd.py`.
4.  **Build the JD parsing page** in `frontend/parse_jd.html`.
5.  **Test the flow**: Log in, upload a JD, then try to parse it. Verify that the (simulated) parsed data is displayed.

---

## 5. Security Notes
- **Authentication and Authorization:** Ensure only authenticated users can parse their own JDs.
- **Data Storage:** Be mindful of how sensitive parsed data is stored. Encryption at rest might be necessary for production environments.
- **Scalability:** For real-world parsing, consider using dedicated NLP services or libraries that can handle various JD formats and extract data accurately and efficiently.

---

This feature is essential for extracting key information from job descriptions, which will be used for more accurate matching and analysis.
