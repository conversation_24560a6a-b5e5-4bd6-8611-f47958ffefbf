<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Sign Up - Dr. Resume</title>
</head>
<body>
    <h2>Create an Account</h2>
    <form id="signupForm">
        <label>Email:</label><br>
        <input type="email" id="email" required><br>
        <label>Password:</label><br>
        <input type="password" id="password" required><br><br>
        <button type="submit">Sign Up</button>
    </form>
    <div id="message"></div>
    <script>
        document.getElementById('signupForm').onsubmit = async function(e) {
            e.preventDefault();
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            const res = await fetch('/api/signup', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ email, password })
            });
            
            const data = await res.json();
            
            if (res.status === 201) {
                document.getElementById('message').innerText = 'Signup successful! You can now log in.';
                window.location.href = '/login.html'; // Redirect to login page
            } else {
                document.getElementById('message').innerText = data.detail || 'An error occurred.';
            }
        };
    </script>
</body>
</html>