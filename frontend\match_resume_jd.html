<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Resume-JD Match - Dr. Resume</title>
</head>
<body>
    <h2>Match Resume with Job Description</h2>
    <form id="matchForm">
        <label for="resumeSelect">Select Resume:</label><br>
        <select id="resumeSelect" required>
            <!-- Resumes will be loaded here dynamically -->
        </select><br><br>

        <label for="jdSelect">Select Job Description:</label><br>
        <select id="jdSelect" required>
            <!-- JDs will be loaded here dynamically -->
        </select><br><br>

        <button type="submit">Get Match Score</button>
    </form>
    <div id="message"></div>
    <div id="matchResult" style="margin-top: 20px;">
        <h3>Match Result:</h3>
        <p><strong>Score:</strong> <span id="score"></span></p>
        <p><strong>Feedback:</strong> <span id="feedback"></span></p>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', async () => {
            const token = localStorage.getItem('token');
            if (!token) {
                document.getElementById('message').innerText = 'You must be logged in to use this feature.';
                return;
            }

            // Function to fetch and populate resumes
            async function loadResumes() {
                const res = await fetch('/api/resumes', {
                    headers: { 'Authorization': 'Bearer ' + token }
                });
                if (res.ok) {
                    const data = await res.json();
                    const resumeSelect = document.getElementById('resumeSelect');
                    resumeSelect.innerHTML = '';
                    data.resumes.forEach(resume => {
                        const option = document.createElement('option');
                        option.value = resume.id;
                        option.innerText = resume.filename;
                        resumeSelect.appendChild(option);
                    });
                } else {
                    document.getElementById('message').innerText = 'Failed to load resumes.';
                }
            }

            // Function to fetch and populate JDs
            async function loadJDs() {
                const res = await fetch('/api/jds', {
                    headers: { 'Authorization': 'Bearer ' + token }
                });
                if (res.ok) {
                    const data = await res.json();
                    const jdSelect = document.getElementById('jdSelect');
                    jdSelect.innerHTML = '';
                    data.jds.forEach(jd => {
                        const option = document.createElement('option');
                        option.value = jd.id;
                        option.innerText = jd.filename;
                        jdSelect.appendChild(option);
                    });
                } else {
                    document.getElementById('message').innerText = 'Failed to load job descriptions.';
                }
            }

            await loadResumes();
            await loadJDs();
        });

        document.getElementById('matchForm').onsubmit = async function(e) {
            e.preventDefault();
            const resumeId = document.getElementById('resumeSelect').value;
            const jdId = document.getElementById('jdSelect').value;
            const token = localStorage.getItem('token');

            if (!resumeId || !jdId) {
                document.getElementById('message').innerText = 'Please select both a resume and a job description.';
                return;
            }

            const res = await fetch('/api/match_resume_jd', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': 'Bearer ' + token
                },
                body: JSON.stringify({ resume_id: parseInt(resumeId), jd_id: parseInt(jdId) })
            });
            
            const data = await res.json();
            
            if (res.ok) {
                document.getElementById('message').innerText = data.message;
                document.getElementById('score').innerText = data.match_score;
                document.getElementById('feedback').innerText = data.feedback;
            } else {
                document.getElementById('message').innerText = data.detail || 'An error occurred during matching.';
                document.getElementById('score').innerText = '';
                document.getElementById('feedback').innerText = '';
            }
        };
    </script>
</body>
</html>