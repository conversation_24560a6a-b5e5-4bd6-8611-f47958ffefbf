# US-4: Job Description (JD) Upload - Dr. Resume AI

This guide explains how to implement the Job Description (JD) upload functionality, allowing authenticated users to upload job description files to the system.

---

## 1. Backend Implementation

### a. Job Description Model
- **File:** `backend/models/jd_model.py`
- **Purpose:** Defines the database table structure for storing job description information, linking it to a user.
- **Key Steps:**
  1. Creates a `JobDescription` model with fields for `id`, `user_id` (foreign key to `User`), `file_path`, and `filename`.
  2. Establishes a relationship with the `User` model.
- **Code:**
```python
from sqlalchemy import Column, Integer, String, ForeignKey
from sqlalchemy.orm import relationship
from ..database import Base

class JobDescription(Base):
    __tablename__ = "job_descriptions"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    file_path = Column(String, unique=True, index=True)
    filename = Column(String)

    owner = relationship("User")
```

### b. Upload JD Endpoint
- **File:** `backend/routers/jd_upload.py`
- **Purpose:** Handles the JD file upload, saves the JD, and records its metadata in the database.
- **Key Steps:**
  1. Requires a valid JWT for authentication.
  2. Extracts the `user_id` from the JWT payload.
  3. Creates an `uploads` directory if it doesn't exist.
  4. Saves the uploaded file to the `uploads` directory.
  5. Creates a new `JobDescription` record in the database with the file path and filename.
- **Code:**
```python
from fastapi import APIRouter, File, UploadFile, HTTPException, status, Depends, Header
from sqlalchemy.orm import Session
from ..database import SessionLocal
from ..models.user_model import User
from ..models.jd_model import JobDescription
from .jwt_utils import verify_token
import shutil
import os

router = APIRouter()

def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

@router.post("/upload_jd")
def upload_jd(file: UploadFile = File(...), Authorization: str = Header(None), db: Session = Depends(get_db)):
    if not Authorization or not Authorization.startswith("Bearer "):
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Missing or invalid token")
    token = Authorization.split(" ")[1]
    payload = verify_token(token)
    if not payload:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid token")
    user_id = payload.get("user_id")

    # Create uploads directory if it doesn't exist
    upload_dir = "uploads"
    os.makedirs(upload_dir, exist_ok=True)

    file_location = os.path.join(upload_dir, file.filename)
    with open(file_location, "wb") as buffer:
        shutil.copyfileobj(file.file, buffer)
    
    new_jd = JobDescription(user_id=user_id, file_path=file.filename, filename=file.filename)
    db.add(new_jd)
    db.commit()
    db.refresh(new_jd)

    return {"filename": file.filename, "message": "Job Description uploaded successfully!"}
```

### c. Registering the Router
- **File:** `backend/main.py`
- **Purpose:** Integrates the JD upload endpoint and the new `JobDescription` model into the FastAPI application.
- **Code Addition:**
```python
from .models import user_model, resume_model, jd_model
from .routers import auth, login, upload, jd_upload

user_model.Base.metadata.create_all(bind=engine)
resume_model.Base.metadata.create_all(bind=engine)
jd_model.Base.metadata.create_all(bind=engine)

app.include_router(jd_upload.router, prefix="/api", tags=["Job Description Upload"])
```

---

## 2. Frontend Implementation

### a. JD Upload Page
- **File:** `frontend/upload_jd.html`
- **Purpose:** Provides a form for users to select and upload their job description files.
- **Features:**
  - File input field that accepts PDF, document, and text formats.
  - An "Upload JD" button.
  - Displays messages indicating upload success or failure.
  - Requires the user to be logged in (checks for JWT in `localStorage`).
- **Code:**
```html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Upload Job Description - Dr. Resume</title>
</head>
<body>
    <h2>Upload Job Description</h2>
    <form id="uploadJdForm">
        <input type="file" id="jdFile" accept=".pdf,.doc,.docx,.txt" required><br><br>
        <button type="submit">Upload JD</button>
    </form>
    <div id="message"></div>
    <script>
        document.getElementById('uploadJdForm').onsubmit = async function(e) {
            e.preventDefault();
            const jdFile = document.getElementById('jdFile').files[0];
            const token = localStorage.getItem('token');

            if (!jdFile) {
                document.getElementById('message').innerText = 'Please select a file to upload.';
                return;
            }

            if (!token) {
                document.getElementById('message').innerText = 'You must be logged in to upload a job description.';
                return;
            }

            const formData = new FormData();
            formData.append('file', jdFile);

            const res = await fetch('/api/upload_jd', {
                method: 'POST',
                headers: {
                    'Authorization': 'Bearer ' + token
                },
                body: formData
            });
            
            const data = await res.json();
            
            if (res.ok) {
                document.getElementById('message').innerText = data.message;
            } else {
                document.getElementById('message').innerText = data.detail || 'An error occurred during upload.';
            }
        };
    </script>
</body>
</html>
```

---

## 3. Integration & Flow
- The user navigates to the `upload_jd.html` page.
- They select a JD file and click "Upload JD".
- The frontend creates a `FormData` object and sends a POST request to `/api/upload_jd` with the file and the user's JWT.
- The backend saves the file to the `uploads` directory and records the file details in the database.
- A success or error message is displayed to the user.

---

## 4. How to Build This Feature (Step-by-Step)
1. **Define the `JobDescription` model** in `backend/models/jd_model.py`.
2. **Update `backend/main.py`** to include `jd_model` metadata creation and register the `jd_upload` router.
3. **Create the upload endpoint** in `backend/routers/jd_upload.py`.
4. **Build the JD upload page** in `frontend/upload_jd.html`.
5. **Test the flow**: Log in, upload a JD, and verify that the file appears in the `uploads` directory and a record is created in the database.

---

## 5. Security Notes
- **Authentication:** Ensure only authenticated users can upload files by verifying the JWT.
- **File Type Validation:** Always perform server-side validation of file types to prevent malicious uploads.
- **File Size Limits:** Implement server-side file size limits to prevent denial-of-service attacks.
- **Directory Traversal:** Be careful when constructing file paths to prevent directory traversal vulnerabilities.

---

This feature allows users to provide job descriptions for analysis and matching with their resumes.
