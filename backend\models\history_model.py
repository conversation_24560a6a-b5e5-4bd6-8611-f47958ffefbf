from sqlalchemy import Column, <PERSON><PERSON>ger, String, Foreign<PERSON>ey, DateTime, Text
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from .database import Base

class History(Base):
    __tablename__ = "history"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    activity_type = Column(String) # e.g., "Resume Upload", "JD Upload", "Resume Parse", "JD Parse", "Match", "Optimize"
    description = Column(Text) # A brief description of the activity
    timestamp = Column(DateTime(timezone=True), server_default=func.now())
    
    # Optional foreign keys to link to specific records
    resume_id = Column(Integer, ForeignKey("resumes.id"), nullable=True)
    jd_id = Column(Integer, ForeignKey("job_descriptions.id"), nullable=True)
    parsed_resume_id = Column(Integer, ForeignKey("parsed_resumes.id"), nullable=True)
    parsed_jd_id = Column(Integer, <PERSON><PERSON><PERSON>("parsed_job_descriptions.id"), nullable=True)
    optimization_id = Column(Integer, Foreign<PERSON>ey("optimization_history.id"), nullable=True)

    user = relationship("User")
    resume = relationship("Resume")
    job_description = relationship("JobDescription")
    parsed_resume = relationship("ParsedResume")
    parsed_jd = relationship("ParsedJobDescription")
    optimization_history = relationship("OptimizationHistory")