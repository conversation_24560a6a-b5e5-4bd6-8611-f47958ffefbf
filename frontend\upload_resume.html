<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Upload Resume - Dr. Resume</title>
</head>
<body>
    <h2>Upload Your Resume</h2>
    <form id="uploadForm">
        <input type="file" id="resumeFile" accept=".pdf,.doc,.docx" required><br><br>
        <button type="submit">Upload Resume</button>
    </form>
    <div id="message"></div>
    <script>
        document.getElementById('uploadForm').onsubmit = async function(e) {
            e.preventDefault();
            const resumeFile = document.getElementById('resumeFile').files[0];
            const token = localStorage.getItem('token');

            if (!resumeFile) {
                document.getElementById('message').innerText = 'Please select a file to upload.';
                return;
            }

            if (!token) {
                document.getElementById('message').innerText = 'You must be logged in to upload a resume.';
                return;
            }

            const formData = new FormData();
            formData.append('file', resumeFile);

            const res = await fetch('/api/upload_resume', {
                method: 'POST',
                headers: {
                    'Authorization': 'Bearer ' + token
                },
                body: formData
            });
            
            const data = await res.json();
            
            if (res.ok) {
                document.getElementById('message').innerText = data.message;
            } else {
                document.getElementById('message').innerText = data.detail || 'An error occurred during upload.';
            }
        };
    </script>
</body>
</html>