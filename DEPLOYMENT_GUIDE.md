# 🚀 Dr. Resume AI - Deployment Guide

## 📋 Pre-Deployment Checklist

### Required Data Replacements

Before deploying to production, you MUST replace these placeholder values:

#### 1. Database Configuration
```bash
# In backend/.env
DATABASE_URL=postgresql://username:password@host:port/database_name
```
**Replace with your actual PostgreSQL connection string**

#### 2. JWT Secret Key
```bash
# In backend/.env
JWT_SECRET_KEY=your-super-secure-random-jwt-secret-key-here
```
**Generate a secure random string (32+ characters)**

#### 3. OpenAI API Key (for Premium Features)
```bash
# In backend/.env
OPENAI_API_KEY=sk-your-actual-openai-api-key-here
```
**Get from: https://platform.openai.com/api-keys**

#### 4. Flask Secret Key
```bash
# In backend/.env
SECRET_KEY=your-super-secure-flask-secret-key-here
```
**Generate a secure random string for session security**

#### 5. CORS Origins
```bash
# In backend/.env
CORS_ORIGINS=https://yourdomain.com,https://www.yourdomain.com
```
**Replace with your actual production domain(s)**

## 🔧 Local Development Setup

### Step 1: Install Dependencies
```bash
# Backend dependencies
cd backend
pip install -r requirements.txt

# Create uploads directory
mkdir -p ../uploads/resumes
mkdir -p ../uploads/job_descriptions
```

### Step 2: Configure Environment
```bash
# Copy and edit environment file
cp .env.example .env
# Edit .env with your actual values
```

### Step 3: Initialize Database
```bash
# Run the application to create tables
python app.py
```

### Step 4: Start Services
```bash
# Terminal 1: Start Backend
cd backend
python app.py

# Terminal 2: Start Frontend
cd frontend
python -m http.server 8000
```

### Step 5: Test Application
```bash
# Run automated tests
python test_application.py

# Manual testing
# Open browser: http://localhost:8000
```

## 🌐 Production Deployment

### Option 1: Traditional Server Deployment

#### Backend Deployment (Flask + Gunicorn)
```bash
# Install production dependencies
pip install gunicorn psycopg2-binary

# Create production configuration
export FLASK_ENV=production
export DATABASE_URL="******************************"

# Run with Gunicorn
gunicorn -w 4 -b 0.0.0.0:5000 app:app
```

#### Frontend Deployment (Nginx)
```nginx
# /etc/nginx/sites-available/dr-resume-ai
server {
    listen 80;
    server_name yourdomain.com;
    
    location / {
        root /path/to/frontend;
        index index.html;
        try_files $uri $uri/ /index.html;
    }
    
    location /api {
        proxy_pass http://localhost:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

### Option 2: Docker Deployment

#### Dockerfile (Backend)
```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY backend/requirements.txt .
RUN pip install -r requirements.txt

COPY backend/ .
COPY uploads/ ../uploads/

EXPOSE 5000
CMD ["gunicorn", "-w", "4", "-b", "0.0.0.0:5000", "app:app"]
```

#### Docker Compose
```yaml
version: '3.8'
services:
  backend:
    build: .
    ports:
      - "5000:5000"
    environment:
      - DATABASE_URL=****************************************
      - JWT_SECRET_KEY=your-secret-key
    depends_on:
      - db
  
  db:
    image: postgres:13
    environment:
      - POSTGRES_DB=resume_ai
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data

  frontend:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./frontend:/usr/share/nginx/html
      - ./nginx.conf:/etc/nginx/nginx.conf

volumes:
  postgres_data:
```

### Option 3: Cloud Platform Deployment

#### Heroku Deployment
```bash
# Install Heroku CLI and login
heroku login

# Create application
heroku create dr-resume-ai

# Add PostgreSQL addon
heroku addons:create heroku-postgresql:hobby-dev

# Set environment variables
heroku config:set JWT_SECRET_KEY="your-secret-key"
heroku config:set OPENAI_API_KEY="your-openai-key"
heroku config:set SECRET_KEY="your-flask-secret"

# Deploy
git push heroku main
```

#### AWS/DigitalOcean/GCP
- Use similar Docker or traditional deployment methods
- Configure load balancers and SSL certificates
- Set up database instances (RDS, Managed PostgreSQL)
- Configure environment variables in platform settings

## 🔒 Security Configuration

### SSL/HTTPS Setup
```bash
# Using Let's Encrypt with Certbot
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d yourdomain.com
```

### Environment Security
```bash
# Set proper file permissions
chmod 600 backend/.env
chown www-data:www-data backend/.env

# Secure upload directories
chmod 755 uploads/
chmod 644 uploads/resumes/*
chmod 644 uploads/job_descriptions/*
```

### Database Security
```sql
-- Create dedicated database user
CREATE USER resume_ai_user WITH PASSWORD 'secure_password';
GRANT CONNECT ON DATABASE resume_ai TO resume_ai_user;
GRANT USAGE ON SCHEMA public TO resume_ai_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO resume_ai_user;
```

## 📊 Monitoring & Maintenance

### Health Checks
```bash
# API health check endpoint
curl http://yourdomain.com/api/health

# Database connection check
python -c "from backend.app import create_app; app = create_app(); print('DB OK')"
```

### Log Monitoring
```bash
# Application logs
tail -f /var/log/dr-resume-ai/app.log

# Nginx access logs
tail -f /var/log/nginx/access.log

# Database logs
tail -f /var/log/postgresql/postgresql.log
```

### Backup Strategy
```bash
# Database backup
pg_dump resume_ai > backup_$(date +%Y%m%d).sql

# File uploads backup
tar -czf uploads_backup_$(date +%Y%m%d).tar.gz uploads/

# Automated backup script
#!/bin/bash
DATE=$(date +%Y%m%d)
pg_dump resume_ai > /backups/db_$DATE.sql
tar -czf /backups/uploads_$DATE.tar.gz uploads/
find /backups -name "*.sql" -mtime +30 -delete
find /backups -name "*.tar.gz" -mtime +30 -delete
```

## 🔧 Troubleshooting

### Common Issues

#### Database Connection Errors
```bash
# Check database status
sudo systemctl status postgresql

# Test connection
psql -h localhost -U username -d database_name

# Check environment variables
echo $DATABASE_URL
```

#### File Upload Issues
```bash
# Check directory permissions
ls -la uploads/
chmod 755 uploads/
chown -R www-data:www-data uploads/

# Check disk space
df -h
```

#### CORS Errors
```bash
# Update CORS origins in .env
CORS_ORIGINS=https://yourdomain.com,https://www.yourdomain.com

# Restart application
sudo systemctl restart dr-resume-ai
```

#### OpenAI API Errors
```bash
# Verify API key
curl -H "Authorization: Bearer $OPENAI_API_KEY" \
     https://api.openai.com/v1/models

# Check usage limits
# Visit: https://platform.openai.com/usage
```

### Performance Optimization

#### Database Optimization
```sql
-- Add indexes for better performance
CREATE INDEX idx_resumes_user_id ON resumes(user_id);
CREATE INDEX idx_job_descriptions_user_id ON job_descriptions(user_id);
CREATE INDEX idx_matching_scores_user_id ON matching_scores(user_id);
CREATE INDEX idx_analytics_user_id ON analytics(user_id);
```

#### Caching Setup
```python
# Add Redis caching for frequent queries
from flask_caching import Cache

cache = Cache(app, config={'CACHE_TYPE': 'redis'})

@cache.memoize(timeout=300)
def get_user_stats(user_id):
    # Cached for 5 minutes
    return calculate_stats(user_id)
```

## ✅ Deployment Verification

### Post-Deployment Checklist

1. **✅ Application Access**
   - [ ] Frontend loads at production URL
   - [ ] All pages render correctly
   - [ ] Navigation works properly

2. **✅ Authentication**
   - [ ] User registration works
   - [ ] Login/logout functionality
   - [ ] JWT token validation

3. **✅ Core Features**
   - [ ] Resume upload and processing
   - [ ] Job description creation
   - [ ] Matching analysis calculation
   - [ ] Suggestions generation

4. **✅ Database**
   - [ ] All tables created
   - [ ] Data persistence works
   - [ ] Relationships maintained

5. **✅ Security**
   - [ ] HTTPS enabled
   - [ ] Environment variables secure
   - [ ] File upload restrictions work
   - [ ] API authentication required

6. **✅ Performance**
   - [ ] Page load times acceptable
   - [ ] File upload speed reasonable
   - [ ] Database queries optimized

## 📞 Support

For deployment issues:
1. Check application logs
2. Verify environment variables
3. Test database connectivity
4. Validate API endpoints
5. Review security settings

**The Dr. Resume AI application is now ready for production deployment!** 🚀
