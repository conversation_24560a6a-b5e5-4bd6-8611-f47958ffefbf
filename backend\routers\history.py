from fastapi import APIRouter, HTTPException, status, Depends, Header
from sqlalchemy.orm import Session
from ..database import SessionLocal
from ..models.history_model import History
from .jwt_utils import verify_token

router = APIRouter()

def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

@router.get("/history")
def get_history(Authorization: str = Header(None), db: Session = Depends(get_db)):
    if not Authorization or not Authorization.startswith("Bearer "):
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Missing or invalid token")
    token = Authorization.split(" ")[1]
    payload = verify_token(token)
    if not payload:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid token")
    user_id = payload.get("user_id")

    user_history = db.query(History).filter(History.user_id == user_id).order_by(History.timestamp.desc()).all()
    
    # Convert history objects to dictionaries for JSON serialization
    history_data = []
    for item in user_history:
        history_data.append({
            "id": item.id,
            "activity_type": item.activity_type,
            "description": item.description,
            "timestamp": item.timestamp.isoformat(),
            "resume_id": item.resume_id,
            "jd_id": item.jd_id,
            "parsed_resume_id": item.parsed_resume_id,
            "parsed_jd_id": item.parsed_jd_id,
            "optimization_id": item.optimization_id,
        })

    return {"history": history_data}