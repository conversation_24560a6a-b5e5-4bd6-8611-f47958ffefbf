from fastapi import APIRouter, HTTPException, status, Depends, Header
from sqlalchemy.orm import Session
from pydantic import BaseModel
from ..database import SessionLocal
from ..models.user_model import User
from ..models.jd_model import JobDescription
from ..models.parsed_jd_model import ParsedJobDescription
from .jwt_utils import verify_token
import json

router = APIRouter()

def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

class ParseJdRequest(BaseModel):
    jd_id: int

@router.post("/parse_jd")
def parse_jd(request: ParseJdRequest, Authorization: str = Header(None), db: Session = Depends(get_db)):
    if not Authorization or not Authorization.startswith("Bearer "):
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Missing or invalid token")
    token = Authorization.split(" ")[1]
    payload = verify_token(token)
    if not payload:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid token")
    user_id = payload.get("user_id")

    jd = db.query(JobDescription).filter(JobDescription.id == request.jd_id, JobDescription.user_id == user_id).first()
    if not jd:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Job Description not found or does not belong to user")

    # Placeholder for actual JD parsing logic
    # In a real application, this would involve calling an NLP service or library
    # For now, we'll simulate some parsed data
    parsed_text_content = f"Extracted text from {jd.filename}: This is a sample job description text content."
    parsed_data_json = {
        "title": "Software Engineer",
        "company": "Acme Corp",
        "location": "Remote",
        "requirements": ["Python", "FastAPI", "SQL", "AWS"],
        "responsibilities": ["Develop and maintain backend services", "Collaborate with frontend team"]
    }
    
    from ..models.history_model import History
    # Check if JD has already been parsed
    existing_parsed_jd = db.query(ParsedJobDescription).filter(ParsedJobDescription.jd_id == jd.id).first()
    if existing_parsed_jd:
        existing_parsed_jd.text_content = parsed_text_content
        existing_parsed_jd.parsed_data = json.dumps(parsed_data_json)
        db.commit()
        db.refresh(existing_parsed_jd)
        parsed_jd_id = existing_parsed_jd.id
    else:
        new_parsed_jd = ParsedJobDescription(
            jd_id=jd.id,
            user_id=user_id,
            text_content=parsed_text_content,
            parsed_data=json.dumps(parsed_data_json)
        )
        db.add(new_parsed_jd)
        db.commit()
        db.refresh(new_parsed_jd)
        parsed_jd_id = new_parsed_jd.id
    
    # Log activity
    new_history_entry = History(
        user_id=user_id,
        activity_type="JD Parse",
        description=f"Parsed Job Description: {jd.filename}",
        jd_id=jd.id,
        parsed_jd_id=parsed_jd_id
    )
    db.add(new_history_entry)
    db.commit()

    return {"success": True, "message": "Job Description parsing initiated!", "parsed_data": parsed_data_json}