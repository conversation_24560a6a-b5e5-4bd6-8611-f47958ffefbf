from fastapi import APIRouter, HTTPException, status, Depends, Header
from sqlalchemy.orm import Session
from pydantic import BaseModel
from ..database import SessionLocal
from ..models.user_model import User
from ..models.resume_model import Resume
from ..models.jd_model import JobDescription
from .jwt_utils import verify_token

router = APIRouter()

def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

class MatchRequest(BaseModel):
    resume_id: int
    jd_id: int

@router.post("/match_resume_jd")
def match_resume_jd(request: MatchRequest, Authorization: str = Header(None), db: Session = Depends(get_db)):
    if not Authorization or not Authorization.startswith("Bearer "):
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Missing or invalid token")
    token = Authorization.split(" ")[1]
    payload = verify_token(token)
    if not payload:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid token")
    user_id = payload.get("user_id")

    # Verify that the resume and JD belong to the authenticated user
    resume = db.query(Resume).filter(Resume.id == request.resume_id, Resume.user_id == user_id).first()
    jd = db.query(JobDescription).filter(JobDescription.id == request.jd_id, JobDescription.user_id == user_id).first()

    if not resume:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Resume not found or does not belong to user")
    if not jd:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Job Description not found or does not belong to user")

    from ..models.history_model import History
    # Placeholder for actual matching logic
    # In a real application, this would involve AI/NLP processing
    match_score = "75%"
    feedback = "This is a placeholder feedback. Implement actual AI matching here."

    # Log activity
    new_history_entry = History(
        user_id=user_id,
        activity_type="Match",
        description=f"Matched resume {resume.filename} with JD {jd.filename}. Score: {match_score}",
        resume_id=resume.id,
        jd_id=jd.id
    )
    db.add(new_history_entry)
    db.commit()

    return {"success": True, "message": "Matching initiated successfully!", "match_score": match_score, "feedback": feedback}