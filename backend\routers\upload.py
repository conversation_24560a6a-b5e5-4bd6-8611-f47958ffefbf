from fastapi import APIRouter, File, UploadFile, HTTPException, status, Depends, Header
from sqlalchemy.orm import Session
from ..database import SessionLocal
from ..models.user_model import User
from ..models.resume_model import Resume
from .jwt_utils import verify_token
import shutil
import os

router = APIRouter()

def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

@router.post("/upload_resume")
def upload_resume(file: UploadFile = File(...), Authorization: str = Header(None), db: Session = Depends(get_db)):
    if not Authorization or not Authorization.startswith("Bearer "):
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Missing or invalid token")
    token = Authorization.split(" ")[1]
    payload = verify_token(token)
    if not payload:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid token")
    user_id = payload.get("user_id")

    # Create uploads directory if it doesn't exist
    upload_dir = "uploads/resumes"
    os.makedirs(upload_dir, exist_ok=True)

    file_location = os.path.join(upload_dir, file.filename)
    with open(file_location, "wb") as buffer:
        shutil.copyfileobj(file.file, buffer)
    
    new_resume = Resume(user_id=user_id, file_path=file_location, filename=file.filename)
    db.add(new_resume)
    db.commit()
    db.refresh(new_resume)

    # Placeholder for local script parsing (US-05 will detail this)
    print(f"Simulating parsing of {file.filename}...")

    return {"filename": file.filename, "message": "Resume uploaded successfully!"}