from fastapi import APIRouter, HTTPException, status, Depends, Header
from sqlalchemy.orm import Session
from pydantic import BaseModel
from ..database import SessionLocal
from ..models.user_model import User
from ..models.resume_model import Resume
from ..models.jd_model import JobDescription
from ..models.optimization_model import OptimizationHistory
from .jwt_utils import verify_token
import json

router = APIRouter()

def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

class OptimizeResumeRequest(BaseModel):
    resume_id: int
    jd_id: int | None = None

@router.post("/optimize_resume")
def optimize_resume(request: OptimizeResumeRequest, Authorization: str = Header(None), db: Session = Depends(get_db)):
    if not Authorization or not Authorization.startswith("Bearer "):
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Missing or invalid token")
    token = Authorization.split(" ")[1]
    payload = verify_token(token)
    if not payload:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid token")
    user_id = payload.get("user_id")

    resume = db.query(Resume).filter(Resume.id == request.resume_id, Resume.user_id == user_id).first()
    if not resume:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Resume not found or does not belong to user")

    jd = None
    if request.jd_id:
        jd = db.query(JobDescription).filter(JobDescription.id == request.jd_id, JobDescription.user_id == user_id).first()
        if not jd:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Job Description not found or does not belong to user")

    # Placeholder for actual resume optimization logic
    # In a real application, this would involve AI/NLP processing based on resume and optionally JD content
    suggestions = [
        "Consider adding more action verbs to your experience descriptions.",
        "Quantify your achievements with numbers and metrics.",
        "Tailor your skills section to match keywords from the job description (if JD provided).",
        "Ensure consistent formatting throughout your resume."
    ]
    if jd:
        suggestions.append(f"Based on {jd.filename}, highlight skills like: Python, FastAPI, SQL.")

    from ..models.history_model import History
    new_optimization_history = OptimizationHistory(
        user_id=user_id,
        resume_id=resume.id,
        jd_id=jd.id if jd else None,
        suggestions=json.dumps(suggestions)
    )
    db.add(new_optimization_history)
    db.commit()
    db.refresh(new_optimization_history)

    # Log activity
    description = f"Optimized resume: {resume.filename}"
    if jd:
        description += f" against JD: {jd.filename}"
    new_history_entry = History(
        user_id=user_id,
        activity_type="Resume Optimization",
        description=description,
        resume_id=resume.id,
        jd_id=jd.id if jd else None,
        optimization_id=new_optimization_history.id
    )
    db.add(new_history_entry)
    db.commit()

    return {"success": True, "message": "Resume optimization initiated!", "suggestions": suggestions}