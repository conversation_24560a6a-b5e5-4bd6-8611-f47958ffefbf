<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Parse Job Description - Dr. Resume</title>
</head>
<body>
    <h2>Parse Job Description</h2>
    <form id="parseJdForm">
        <label for="jdSelect">Select Job Description to Parse:</label><br>
        <select id="jdSelect" required>
            <!-- JDs will be loaded here dynamically -->
        </select><br><br>

        <button type="submit">Parse JD</button>
    </form>
    <div id="message"></div>
    <div id="parsedResult" style="margin-top: 20px;">
        <h3>Parsed Data:</h3>
        <pre id="parsedDataDisplay"></pre>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', async () => {
            const token = localStorage.getItem('token');
            if (!token) {
                document.getElementById('message').innerText = 'You must be logged in to use this feature.';
                return;
            }

            // Function to fetch and populate JDs
            async function loadJDs() {
                const res = await fetch('/api/jds', {
                    headers: { 'Authorization': 'Bearer ' + token }
                });
                if (res.ok) {
                    const data = await res.json();
                    const jdSelect = document.getElementById('jdSelect');
                    jdSelect.innerHTML = '';
                    if (data.jds.length === 0) {
                        document.getElementById('message').innerText = 'No job descriptions uploaded yet. Please upload one first.';
                        return;
                    }
                    data.jds.forEach(jd => {
                        const option = document.createElement('option');
                        option.value = jd.id;
                        option.innerText = jd.filename;
                        jdSelect.appendChild(option);
                    });
                } else {
                    document.getElementById('message').innerText = 'Failed to load job descriptions.';
                }
            }

            await loadJDs();
        });

        document.getElementById('parseJdForm').onsubmit = async function(e) {
            e.preventDefault();
            const jdId = document.getElementById('jdSelect').value;
            const token = localStorage.getItem('token');

            if (!jdId) {
                document.getElementById('message').innerText = 'Please select a job description to parse.';
                return;
            }

            const res = await fetch('/api/parse_jd', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': 'Bearer ' + token
                },
                body: JSON.stringify({ jd_id: parseInt(jdId) })
            });
            
            const data = await res.json();
            
            if (res.ok) {
                document.getElementById('message').innerText = data.message;
                document.getElementById('parsedDataDisplay').innerText = JSON.stringify(data.parsed_data, null, 2);
            } else {
                document.getElementById('message').innerText = data.detail || 'An error occurred during parsing.';
                document.getElementById('parsedDataDisplay').innerText = '';
            }
        };
    </script>
</body>
</html>